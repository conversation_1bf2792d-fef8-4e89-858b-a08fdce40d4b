# Permission Module

The Permission Module provides subscription-based permission checking for the EduSG application. It allows you to control access to features and functionality based on the user's subscription package.

## Overview

This module works alongside the existing RBAC (Role-Based Access Control) system to provide an additional layer of permissions based on subscription packages. While RBAC controls access based on user roles (ADMIN, TEACHER, etc.), the Permission Module controls access based on what features and limits are included in the user's subscription package.

## Architecture

### Core Components

1. **PermissionService** - Main service for checking permissions
2. **PermissionGuard** - Guard for declarative permission checking
3. **Decorators** - `@RequiresFeature()` and `@RequiresAccess()` for endpoint protection
4. **Interfaces** - Type definitions for permission structures

### Permission Structure

Permissions are stored in the Package entity's `permissions` JSONB column with this structure:

```json
{
  "features": ["basic_worksheets", "exam_creation", "student_management"],
  "limits": {
    "maxStudents": 100,
    "maxWorksheets": 50,
    "maxExams": 25
  },
  "access": {
    "adminPanel": true,
    "analytics": true,
    "advancedReports": false
  }
}
```

## Usage

### 1. Import the Module

```typescript
import { PermissionModule } from './modules/permission';

@Module({
  imports: [PermissionModule],
  // ...
})
export class YourModule {}
```

### 2. Using Decorators (Recommended)

The easiest way to protect endpoints is using decorators:

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { AuthGuard } from '../auth/guards/auth.guard';
import { PermissionGuard, RequiresFeature, RequiresAccess } from '../permission';

@Controller('exams')
@UseGuards(AuthGuard, PermissionGuard) // Apply both guards
export class ExamController {
  
  @Get()
  @RequiresFeature('exam_creation')
  async createExam() {
    // Only users with 'exam_creation' feature can access
  }

  @Get('advanced')
  @RequiresFeature('exam_creation', 'student_management')
  @RequiresAccess('analytics')
  async advancedExamAnalytics() {
    // User must have both features AND analytics access
  }
}
```

### 3. Using PermissionService Directly

For complex permission logic, inject and use the service directly:

```typescript
import { Injectable } from '@nestjs/common';
import { PermissionService } from '../permission';

@Injectable()
export class ExamService {
  constructor(private readonly permissionService: PermissionService) {}

  async createExam(userId: string) {
    // Check if user has the feature
    const canCreateExams = await this.permissionService.hasFeature(userId, 'exam_creation');
    if (!canCreateExams) {
      throw new ForbiddenException('Exam creation not available in your subscription');
    }

    // Check usage against limits
    const currentExamCount = await this.getCurrentExamCount(userId);
    const canAddMore = await this.permissionService.checkLimit(userId, 'maxExams', currentExamCount);
    if (!canAddMore) {
      throw new ForbiddenException('Exam limit reached for your subscription');
    }

    // Proceed with exam creation
  }
}
```

## API Reference

### PermissionService Methods

#### `getUserPermissions(userId: string): Promise<UserPermissions>`
Get all permissions for a user based on their subscription.

#### `hasFeature(userId: string, feature: string): Promise<boolean>`
Check if user has a specific feature.

#### `getLimit(userId: string, limitKey: string): Promise<number | null>`
Get a specific limit value for a user.

#### `hasAccess(userId: string, accessKey: string): Promise<boolean>`
Check if user has a specific access permission.

#### `checkLimit(userId: string, limitKey: string, currentUsage: number): Promise<boolean>`
Check if user is within a specific limit.

#### `hasActiveSubscription(userId: string): Promise<boolean>`
Check if user has an active subscription.

### Decorators

#### `@RequiresFeature(...features: string[])`
Requires user to have all specified features.

#### `@RequiresAccess(...accessKeys: string[])`
Requires user to have all specified access permissions.

## Default Permissions

Users without active subscriptions receive default permissions:

```typescript
{
  features: ['basic_worksheets'],
  limits: {
    maxStudents: 5,
    maxWorksheets: 3,
    maxExams: 1,
  },
  access: {
    adminPanel: false,
    analytics: false,
    advancedReports: false,
  },
  hasActiveSubscription: false,
}
```

## Integration with RBAC

The Permission Module works alongside the existing RBAC system:

```typescript
@Controller('admin')
@UseGuards(AuthGuard, RoleGuard, PermissionGuard)
export class AdminController {
  
  @Get('advanced-reports')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER) // Role-based check
  @RequiresAccess('advancedReports') // Subscription-based check
  async getAdvancedReports() {
    // User must be ADMIN/SCHOOL_MANAGER AND have advanced reports access
  }
}
```

## Error Handling

The Permission Module handles errors gracefully:

- Users without subscriptions get default permissions
- Invalid permission keys return safe defaults (false/null)
- Service errors are logged and return safe defaults
- Guards deny access on any errors

## Testing

The module includes comprehensive unit tests. Run them with:

```bash
npm test -- --testPathPattern=permission.service.spec.ts
```

## Examples

The Permission Module provides comprehensive functionality for subscription-based access control. Refer to the existing controllers in the codebase for implementation examples.

## Best Practices

1. **Always use guards**: Apply `AuthGuard` before `PermissionGuard`
2. **Combine with RBAC**: Use both role and subscription checks when needed
3. **Check limits**: Always validate current usage against limits
4. **Handle gracefully**: Provide clear error messages for permission failures
5. **Log access**: The service automatically logs permission checks for auditing
