# Usage Tracking Module

The Usage Tracking Module provides Redis-based usage tracking functionality that integrates with the existing subscription-based permissions system. It allows you to track user usage against defined limits (e.g., daily, weekly, monthly counts) and check if limits have been reached.

## Overview

This module works alongside the existing Permission Module to provide real-time usage tracking. While the Permission Module defines what limits a user has based on their subscription package, the Usage Tracking Module tracks actual usage and enforces those limits.

## Features

- **Redis-based storage** for high performance and scalability
- **Multiple time periods** support (daily, weekly, monthly)
- **Atomic operations** to prevent race conditions
- **Automatic TTL management** to prevent memory leaks
- **Integration with Permission Module** for limit checking
- **Graceful error handling** with fallback behavior
- **Comprehensive logging** for debugging and monitoring

## Architecture

### Core Components

1. **UsageTrackingService** - Main service for tracking and checking usage
2. **UsageTrackingModule** - NestJS module configuration
3. **Interfaces** - Type definitions for usage tracking
4. **Examples** - Sample controller showing usage patterns

### Redis Key Structure

The service uses structured Redis keys with automatic TTL:

- Daily: `usage:daily:YYYY-MM-DD:userId:feature`
- Weekly: `usage:weekly:YYYY-WW:userId:feature`
- Monthly: `usage:monthly:YYYY-MM:userId:feature`

### TTL Configuration

- Daily keys: 2 days (to handle timezone differences)
- Weekly keys: 8 days
- Monthly keys: 32 days

## Installation

The module is already integrated into the main application. To use it in your service:

```typescript
import { UsageTrackingService } from '../usage-tracking/usage-tracking.service';

@Injectable()
export class YourService {
  constructor(
    private readonly usageTrackingService: UsageTrackingService,
  ) {}
}
```

## API Reference

### UsageTrackingService Methods

#### `incrementUsage(userId, feature, amount?, period?): Promise<number>`

Increment usage count for a user and feature.

```typescript
// Increment daily worksheet usage by 1
const newCount = await this.usageTrackingService.incrementUsage(
  'user-123',
  'maxWorksheets',
  1,
  'daily'
);
```

#### `getCurrentUsage(userId, feature, period?): Promise<number>`

Get current usage count for a user and feature.

```typescript
// Get current daily worksheet usage
const currentUsage = await this.usageTrackingService.getCurrentUsage(
  'user-123',
  'maxWorksheets',
  'daily'
);
```

#### `checkUsageLimit(userId, feature, period?): Promise<UsageLimitCheckResult>`

Check usage against limit with detailed information.

```typescript
// Check if user can create more worksheets
const limitCheck = await this.usageTrackingService.checkUsageLimit(
  'user-123',
  'maxWorksheets',
  'daily'
);

console.log(limitCheck);
// {
//   withinLimit: true,
//   current: 5,
//   limit: 10,
//   remaining: 5,
//   period: 'daily',
//   feature: 'maxWorksheets'
// }
```

#### `isUsageLimitReached(userId, feature, period?): Promise<boolean>`

Simple boolean check if usage limit has been reached.

```typescript
// Check if worksheet limit is reached
const limitReached = await this.usageTrackingService.isUsageLimitReached(
  'user-123',
  'maxWorksheets',
  'daily'
);
```

#### `resetUsage(userId, feature, period?): Promise<boolean>`

Reset usage count (useful for testing or admin operations).

```typescript
// Reset daily worksheet usage
const resetSuccess = await this.usageTrackingService.resetUsage(
  'user-123',
  'maxWorksheets',
  'daily'
);
```

## Usage Patterns

### Basic Usage Tracking

```typescript
@Injectable()
export class WorksheetService {
  constructor(
    private readonly usageTrackingService: UsageTrackingService,
  ) {}

  async createWorksheet(userId: string, worksheetData: any) {
    // Check if user can create more worksheets
    const limitCheck = await this.usageTrackingService.checkUsageLimit(
      userId,
      'maxWorksheets',
      'daily'
    );

    if (!limitCheck.withinLimit) {
      throw new BadRequestException(
        `Daily worksheet limit reached (${limitCheck.current}/${limitCheck.limit})`
      );
    }

    // Create the worksheet
    const worksheet = await this.createWorksheetInDatabase(worksheetData);

    // Increment usage count
    await this.usageTrackingService.incrementUsage(
      userId,
      'maxWorksheets',
      1,
      'daily'
    );

    return worksheet;
  }
}
```

### Guard-based Usage Checking

```typescript
@Injectable()
export class UsageLimitGuard implements CanActivate {
  constructor(
    private readonly usageTrackingService: UsageTrackingService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    
    // Get feature from metadata or request
    const feature = 'maxWorksheets'; // This could come from decorator
    
    const limitReached = await this.usageTrackingService.isUsageLimitReached(
      user.sub,
      feature,
      'daily'
    );

    return !limitReached;
  }
}
```

### Multiple Period Tracking

```typescript
// Track usage across different periods
async trackMultiPeriodUsage(userId: string, feature: string) {
  await Promise.all([
    this.usageTrackingService.incrementUsage(userId, feature, 1, 'daily'),
    this.usageTrackingService.incrementUsage(userId, feature, 1, 'weekly'),
    this.usageTrackingService.incrementUsage(userId, feature, 1, 'monthly'),
  ]);
}
```

## Integration with Permission Module

The Usage Tracking Service automatically integrates with the Permission Module to get user limits:

```typescript
// The service automatically gets limits from user's subscription
const limitCheck = await this.usageTrackingService.checkUsageLimit(
  userId,
  'maxWorksheets'
);
// Internally calls: this.permissionService.getLimit(userId, 'maxWorksheets')
```

## Error Handling

The service includes comprehensive error handling:

- **Redis connection failures**: Returns safe defaults (0 usage, deny access)
- **Invalid periods**: Throws descriptive errors
- **Missing permissions**: Logs warnings and denies access
- **Concurrent operations**: Uses atomic Redis operations

## Testing

Run the tests with:

```bash
npm test -- --testPathPattern=usage-tracking.service.spec.ts
```

## Examples

The Usage Tracking Module provides comprehensive functionality for monitoring and limiting feature usage. Refer to the existing controllers in the codebase for implementation examples.

## Configuration

The service uses default configuration that can be customized by modifying the `DEFAULT_USAGE_TRACKING_CONFIG` in the interfaces file.

## Monitoring

The service includes detailed logging for monitoring usage patterns and debugging issues. All operations are logged with appropriate log levels.
