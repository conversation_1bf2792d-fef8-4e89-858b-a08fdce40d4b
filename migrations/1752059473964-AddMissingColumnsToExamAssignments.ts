import { MigrationInterface, QueryRunner } from "typeorm";

export class AddMissingColumnsToExamAssignments1752059473964 implements MigrationInterface {
    name = 'AddMissingColumnsToExamAssignments1752059473964'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "prices" DROP CONSTRAINT "FK_prices_package"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_subscriptions_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_prices_stripePriceId"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_prices_packageId"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_package_stripeProductId"`);
        await queryRunner.query(`ALTER TABLE "exam_assignments" DROP CONSTRAINT "UQ_exam_assignments_examId_studentId"`);
        await queryRunner.query(`ALTER TABLE "invitations" ADD "deletedAt" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "invitations" ADD "role" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "invitations" ADD "invitedBy" character varying`);
        await queryRunner.query(`ALTER TABLE "exam_assignments" ADD "deletedAt" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "exam_assignments" ADD "score" integer NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "exam_assignments" ADD "feedback" character varying(500)`);
        await queryRunner.query(`ALTER TYPE "public"."invitations_status_enum" RENAME TO "invitations_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."invitations_status_enum" AS ENUM('pending', 'accepted', 'rejected', 'expired')`);
        await queryRunner.query(`ALTER TABLE "invitations" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "invitations" ALTER COLUMN "status" TYPE "public"."invitations_status_enum" USING "status"::"text"::"public"."invitations_status_enum"`);
        await queryRunner.query(`ALTER TABLE "invitations" ALTER COLUMN "status" SET DEFAULT 'pending'`);
        await queryRunner.query(`DROP TYPE "public"."invitations_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "invitations" ADD CONSTRAINT "UQ_e577dcf9bb6d084373ed3998509" UNIQUE ("token")`);
        await queryRunner.query(`ALTER TABLE "invitations" DROP COLUMN "expiresAt"`);
        await queryRunner.query(`ALTER TABLE "invitations" ADD "expiresAt" TIMESTAMP WITH TIME ZONE NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_d0a95ef8a28188364c546eb65c" ON "subscriptions" ("user_id") `);
        await queryRunner.query(`ALTER TABLE "exam_assignments" ADD CONSTRAINT "UQ_406ab0f9cb7deb7098bf3a6343f" UNIQUE ("examId", "studentId")`);
        await queryRunner.query(`ALTER TABLE "prices" ADD CONSTRAINT "FK_0e877d1d7f230ec3cd8a8277bdf" FOREIGN KEY ("packageId") REFERENCES "package"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "invitations" ADD CONSTRAINT "FK_e4887a308d5720f4a3667f2b928" FOREIGN KEY ("schoolId") REFERENCES "schools"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "exam_assignments" ADD CONSTRAINT "FK_fcbd31d019d715a0f87c186d03d" FOREIGN KEY ("examId") REFERENCES "exams"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "exam_assignments" ADD CONSTRAINT "FK_a69d68cda86042f364a19297190" FOREIGN KEY ("studentId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "exam_assignments" DROP CONSTRAINT "FK_a69d68cda86042f364a19297190"`);
        await queryRunner.query(`ALTER TABLE "exam_assignments" DROP CONSTRAINT "FK_fcbd31d019d715a0f87c186d03d"`);
        await queryRunner.query(`ALTER TABLE "invitations" DROP CONSTRAINT "FK_e4887a308d5720f4a3667f2b928"`);
        await queryRunner.query(`ALTER TABLE "prices" DROP CONSTRAINT "FK_0e877d1d7f230ec3cd8a8277bdf"`);
        await queryRunner.query(`ALTER TABLE "exam_assignments" DROP CONSTRAINT "UQ_406ab0f9cb7deb7098bf3a6343f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d0a95ef8a28188364c546eb65c"`);
        await queryRunner.query(`ALTER TABLE "invitations" DROP COLUMN "expiresAt"`);
        await queryRunner.query(`ALTER TABLE "invitations" ADD "expiresAt" TIMESTAMP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "invitations" DROP CONSTRAINT "UQ_e577dcf9bb6d084373ed3998509"`);
        await queryRunner.query(`CREATE TYPE "public"."invitations_status_enum_old" AS ENUM('pending', 'accepted')`);
        await queryRunner.query(`ALTER TABLE "invitations" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "invitations" ALTER COLUMN "status" TYPE "public"."invitations_status_enum_old" USING "status"::"text"::"public"."invitations_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "invitations" ALTER COLUMN "status" SET DEFAULT 'pending'`);
        await queryRunner.query(`DROP TYPE "public"."invitations_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."invitations_status_enum_old" RENAME TO "invitations_status_enum"`);
        await queryRunner.query(`ALTER TABLE "exam_assignments" DROP COLUMN "feedback"`);
        await queryRunner.query(`ALTER TABLE "exam_assignments" DROP COLUMN "score"`);
        await queryRunner.query(`ALTER TABLE "exam_assignments" DROP COLUMN "deletedAt"`);
        await queryRunner.query(`ALTER TABLE "invitations" DROP COLUMN "invitedBy"`);
        await queryRunner.query(`ALTER TABLE "invitations" DROP COLUMN "role"`);
        await queryRunner.query(`ALTER TABLE "invitations" DROP COLUMN "deletedAt"`);
        await queryRunner.query(`ALTER TABLE "exam_assignments" ADD CONSTRAINT "UQ_exam_assignments_examId_studentId" UNIQUE ("examId", "studentId")`);
        await queryRunner.query(`CREATE INDEX "IDX_package_stripeProductId" ON "package" ("stripeProductId") `);
        await queryRunner.query(`CREATE INDEX "IDX_prices_packageId" ON "prices" ("packageId") `);
        await queryRunner.query(`CREATE INDEX "IDX_prices_stripePriceId" ON "prices" ("stripePriceId") `);
        await queryRunner.query(`CREATE INDEX "IDX_subscriptions_user_id" ON "subscriptions" ("user_id") `);
        await queryRunner.query(`ALTER TABLE "prices" ADD CONSTRAINT "FK_prices_package" FOREIGN KEY ("packageId") REFERENCES "package"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

}
