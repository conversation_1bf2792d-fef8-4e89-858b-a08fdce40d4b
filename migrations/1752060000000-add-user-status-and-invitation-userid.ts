import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserStatusAndInvitationUserId1752060000000 implements MigrationInterface {
  name = 'AddUserStatusAndInvitationUserId1752060000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create user status enum type
    await queryRunner.query(
      `CREATE TYPE "public"."users_status_enum" AS ENUM('invited', 'active')`
    );

    // Add status column to users table with default 'active'
    await queryRunner.query(
      `ALTER TABLE "users" ADD "status" "public"."users_status_enum" NOT NULL DEFAULT 'active'`
    );

    // Make name column nullable in users table
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "name" DROP NOT NULL`
    );

    // Make password column nullable in users table
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "password" DROP NOT NULL`
    );

    // Add userId column to invitations table
    await queryRunner.query(
      `ALTER TABLE "invitations" ADD "userId" uuid`
    );

    // Add foreign key constraint for userId in invitations table
    await queryRunner.query(
      `ALTER TABLE "invitations" ADD CONSTRAINT "FK_invitations_userId" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove foreign key constraint
    await queryRunner.query(
      `ALTER TABLE "invitations" DROP CONSTRAINT "FK_invitations_userId"`
    );

    // Remove userId column from invitations table
    await queryRunner.query(
      `ALTER TABLE "invitations" DROP COLUMN "userId"`
    );

    // Make password column not nullable in users table (this might fail if there are null values)
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "password" SET NOT NULL`
    );

    // Make name column not nullable in users table (this might fail if there are null values)
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "name" SET NOT NULL`
    );

    // Remove status column from users table
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "status"`
    );

    // Drop user status enum type
    await queryRunner.query(
      `DROP TYPE "public"."users_status_enum"`
    );
  }
}
