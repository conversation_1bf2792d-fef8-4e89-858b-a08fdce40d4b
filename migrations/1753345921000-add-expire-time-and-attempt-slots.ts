import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddExpireTimeAndAttemptSlots1753345921000 implements MigrationInterface {
  name = 'AddExpireTimeAndAttemptSlots1753345921000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add expiresAt column to exam_assignments
    await queryRunner.query(`
      ALTER TABLE "exam_assignments" 
      ADD COLUMN "expiresAt" TIMESTAMP WITH TIME ZONE NULL
    `);

    // Add totalAttempts column to exam_assignments with default value of 1
    await queryRunner.query(`
      ALTER TABLE "exam_assignments" 
      ADD COLUMN "totalAttempts" INTEGER NOT NULL DEFAULT 1
    `);

    // Add startedAt column to exam_results
    await queryRunner.query(`
      ALTER TABLE "exam_results" 
      ADD COLUMN "startedAt" TIMESTAMP WITH TIME ZONE NULL
    `);

    // Add comment to explain the new columns
    await queryRunner.query(`
      COMMENT ON COLUMN "exam_assignments"."expiresAt" IS 'The date and time when the exam assignment expires'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "exam_assignments"."totalAttempts" IS 'The total number of attempts allowed for this assignment'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "exam_results"."startedAt" IS 'The timestamp when the student started this specific exam attempt'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove columns in reverse order
    await queryRunner.query(`
      ALTER TABLE "exam_results" DROP COLUMN "startedAt"
    `);

    await queryRunner.query(`
      ALTER TABLE "exam_assignments" DROP COLUMN "totalAttempts"
    `);

    await queryRunner.query(`
      ALTER TABLE "exam_assignments" DROP COLUMN "expiresAt"
    `);
  }
} 