/**
 * Migration Script: Consolidate Difficulty Field
 * 
 * This script migrates data from difficultyLevel to difficulty field
 * and removes the difficultyLevel field from all documents in the questionpools collection.
 * 
 * Run with: npx ts-node scripts/migrations/consolidate-difficulty-field.migration.ts
 */

import { MongoClient } from 'mongodb';
import * as dotenv from 'dotenv';
import { resolve } from 'path';

// Load environment variables
dotenv.config({ path: resolve(__dirname, '../../.env') });

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/edusg';
const DATABASE_NAME = process.env.DB_NAME || 'edusg';
const COLLECTION_NAME = 'questionpools';

interface MigrationStats {
  totalDocuments: number;
  documentsWithDifficultyLevel: number;
  documentsUpdated: number;
  documentsWithBothFields: number;
  documentsWithConflict: number;
  errors: Array<{ id: string; error: string }>;
}

async function runMigration(): Promise<void> {
  const client = new MongoClient(MONGODB_URI);
  const stats: MigrationStats = {
    totalDocuments: 0,
    documentsWithDifficultyLevel: 0,
    documentsUpdated: 0,
    documentsWithBothFields: 0,
    documentsWithConflict: 0,
    errors: []
  };

  try {
    console.log('🚀 Starting difficulty field consolidation migration...');
    console.log(`📍 Connecting to MongoDB at: ${MONGODB_URI}`);
    
    await client.connect();
    console.log('✅ Connected to MongoDB');

    const db = client.db(DATABASE_NAME);
    const collection = db.collection(COLLECTION_NAME);

    // Count total documents
    stats.totalDocuments = await collection.countDocuments();
    console.log(`📊 Total documents in collection: ${stats.totalDocuments}`);

    // Find documents with difficultyLevel field
    const documentsWithDifficultyLevel = await collection.find({ 
      difficultyLevel: { $exists: true } 
    }).toArray();
    
    stats.documentsWithDifficultyLevel = documentsWithDifficultyLevel.length;
    console.log(`📋 Documents with difficultyLevel field: ${stats.documentsWithDifficultyLevel}`);

    // Process each document
    for (const doc of documentsWithDifficultyLevel) {
      try {
        const updateOperations: any = {};
        
        // Check if both fields exist
        if (doc.difficulty && doc.difficultyLevel) {
          stats.documentsWithBothFields++;
          
          // Check for conflicts
          if (doc.difficulty !== doc.difficultyLevel) {
            stats.documentsWithConflict++;
            console.warn(`⚠️  Conflict found in document ${doc._id}:`);
            console.warn(`   - difficulty: ${doc.difficulty}`);
            console.warn(`   - difficultyLevel: ${doc.difficultyLevel}`);
            console.warn(`   → Using difficultyLevel value: ${doc.difficultyLevel}`);
          }
        }

        // Copy difficultyLevel to difficulty if needed
        if (doc.difficultyLevel) {
          updateOperations.$set = { difficulty: doc.difficultyLevel };
        }

        // Remove difficultyLevel field
        updateOperations.$unset = { difficultyLevel: "" };

        // Update the document
        await collection.updateOne(
          { _id: doc._id },
          updateOperations
        );

        stats.documentsUpdated++;
        
        if (stats.documentsUpdated % 100 === 0) {
          console.log(`⏳ Progress: ${stats.documentsUpdated}/${stats.documentsWithDifficultyLevel} documents updated`);
        }
      } catch (error) {
        stats.errors.push({
          id: doc._id.toString(),
          error: error instanceof Error ? error.message : String(error)
        });
        console.error(`❌ Error updating document ${doc._id}:`, error);
      }
    }

    // Final cleanup: Remove difficultyLevel from any remaining documents
    const cleanupResult = await collection.updateMany(
      { difficultyLevel: { $exists: true } },
      { $unset: { difficultyLevel: "" } }
    );

    console.log(`\n✅ Migration completed successfully!`);
    console.log(`📊 Migration Statistics:`);
    console.log(`   - Total documents: ${stats.totalDocuments}`);
    console.log(`   - Documents with difficultyLevel: ${stats.documentsWithDifficultyLevel}`);
    console.log(`   - Documents updated: ${stats.documentsUpdated}`);
    console.log(`   - Documents with both fields: ${stats.documentsWithBothFields}`);
    console.log(`   - Documents with conflicts: ${stats.documentsWithConflict}`);
    console.log(`   - Cleanup operation modified: ${cleanupResult.modifiedCount} documents`);
    
    if (stats.errors.length > 0) {
      console.log(`\n⚠️  Errors encountered: ${stats.errors.length}`);
      stats.errors.forEach(err => {
        console.log(`   - Document ${err.id}: ${err.error}`);
      });
    }

    // Verify migration
    const remainingDocsWithDifficultyLevel = await collection.countDocuments({
      difficultyLevel: { $exists: true }
    });

    if (remainingDocsWithDifficultyLevel === 0) {
      console.log('\n✅ Verification passed: No documents with difficultyLevel field remaining');
    } else {
      console.error(`\n❌ Verification failed: ${remainingDocsWithDifficultyLevel} documents still have difficultyLevel field`);
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await client.close();
    console.log('\n🔒 Database connection closed');
  }
}

// Add dry-run capability
async function dryRun(): Promise<void> {
  const client = new MongoClient(MONGODB_URI);

  try {
    console.log('🔍 Running migration in DRY-RUN mode...\n');
    
    await client.connect();
    const db = client.db(DATABASE_NAME);
    const collection = db.collection(COLLECTION_NAME);

    const documentsWithDifficultyLevel = await collection.find({ 
      difficultyLevel: { $exists: true } 
    }).limit(10).toArray();

    console.log(`📋 Sample of documents that would be affected (showing first 10):\n`);
    
    documentsWithDifficultyLevel.forEach((doc, index) => {
      console.log(`Document ${index + 1} (ID: ${doc._id}):`);
      console.log(`  - Current difficulty: ${doc.difficulty || 'undefined'}`);
      console.log(`  - Current difficultyLevel: ${doc.difficultyLevel}`);
      console.log(`  - Action: Set difficulty to "${doc.difficultyLevel}", remove difficultyLevel field`);
      if (doc.difficulty && doc.difficulty !== doc.difficultyLevel) {
        console.log(`  ⚠️  CONFLICT: Will override difficulty value`);
      }
      console.log('');
    });

    const totalCount = await collection.countDocuments({ 
      difficultyLevel: { $exists: true } 
    });
    console.log(`📊 Total documents to be migrated: ${totalCount}`);

  } finally {
    await client.close();
  }
}

// Main execution
if (require.main === module) {
  const args = process.argv.slice(2);
  const isDryRun = args.includes('--dry-run') || args.includes('-d');

  if (isDryRun) {
    dryRun().catch(console.error);
  } else {
    console.log('⚠️  This migration will modify your database!');
    console.log('   Run with --dry-run flag to preview changes\n');
    
    // Add a 5 second delay before starting
    console.log('Starting migration in 5 seconds... (Press Ctrl+C to cancel)');
    setTimeout(() => {
      runMigration().catch(console.error);
    }, 5000);
  }
}

export { runMigration, dryRun };
