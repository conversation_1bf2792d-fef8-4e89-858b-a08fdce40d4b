import { forwardRef, Modu<PERSON> } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { UserModule } from '../user/user.module';
import { SchoolModule } from '../school/school.module';
import { JwtStrategy } from './strategies/jwt.strategy';
import { AuthService } from './auth.service';
import { RbacService } from './services/rbac.service';
import { InvitationModule } from '../invitation/invitation.module';
@Module({
  imports: [
    forwardRef(() => UserModule),
    forwardRef(() => SchoolModule),
    PassportModule,
    forwardRef(() => InvitationModule),
    JwtModule.registerAsync({
      useFactory: () => ({
        secret: process.env.JWT_SECRET, // Ensure this matches the .env value
        signOptions: { expiresIn: '30d' }, // Set expiration to 1 month
      }),
    }),
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy, RbacService],
  exports: [AuthService, JwtModule, RbacService],
})
export class AuthModule {}
