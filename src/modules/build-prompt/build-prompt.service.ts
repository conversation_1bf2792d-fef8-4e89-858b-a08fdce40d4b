import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserRequest } from '../prompt/dto/genPrompt.dto';
import {
  SvgPromptContext,
  SvgStrategyFactory,
  TopicInstructionsFactory,
  ExerciseTypeFactory
} from './strategies';

@Injectable()
export class BuildPromptService {
  private readonly logger = new Logger(BuildPromptService.name);

  constructor(
    private configService: ConfigService,
  ) {}

  /**
   * Builds a system prompt for generating educational exercises
   * @param contextContent Curriculum content from official documents
   * @param metadataString Formatted metadata about the exercise requirements
   * @param userRequest User's specific request parameters
   * @returns A complete system prompt for the AI
   */
  /**
   * Builds a system prompt for generating educational exercises
   * @param contextContent Curriculum content from official documents
   * @param metadataString Formatted metadata about the exercise requirements
   * @param userRequest User's specific request parameters
   * @param examinationFormatText
   * @returns A complete system prompt for the AI
   */
  buildSystemPrompt(
      contextContent: string,
      metadataString: string,
      userRequest: UserRequest,
      examinationFormatText?: string,
  ): string {
    const exerciseTypeStr = userRequest.exerciseType.join(', ');

    // Use factories to create strategies
    const topicStrategy = TopicInstructionsFactory.createStrategy(userRequest.topic);
    const exerciseTypeStrategy = ExerciseTypeFactory.createStrategy(userRequest.exerciseType);

    // Get instructions from strategies
    const topicSpecificInstructions = topicStrategy.generateInstructions();
    const exerciseTypeInstructions = exerciseTypeStrategy.generateInstructions();

    // Build subject expertise statement based on the subject hierarchy:
    // topic: Main subject area (e.g., "Mathematics")
    // parentSubject: Chapter/unit (e.g., "Fractions")
    // subject: Specific lesson content (e.g., "Dividing a whole number by a proper fraction")
    const subjectExpertise = userRequest.subject
      ? `You are an expert educational content creator specializing in ${userRequest.subject}${userRequest.parentSubject ? ` within the ${userRequest.parentSubject} chapter` : ''}${userRequest.topic ? ` of ${userRequest.topic}` : ''}.`
      : `You are an expert educational content creator specializing in creating high-quality ${userRequest.difficulty} level exercises for ${userRequest.topic}${userRequest.parentSubject ? ` focusing on ${userRequest.parentSubject}` : ''} at ${userRequest.grade} level.`;

    // Determine if we need to emphasize specific learning objectives based on subject hierarchy
    const learningObjectives = userRequest.subject && userRequest.parentSubject
      ? `Focus on developing students' understanding of ${userRequest.subject} concepts within the broader context of ${userRequest.parentSubject}.`
      : userRequest.subject
        ? `Focus on developing students' understanding of ${userRequest.subject} concepts.`
        : userRequest.parentSubject
          ? `Focus on developing students' understanding of key concepts within ${userRequest.parentSubject}.`
          : `Focus on developing students' understanding of key ${userRequest.topic} concepts appropriate for ${userRequest.grade} level.`;

    const prompt = `
    # ROLE AND EXPERTISE
    ${subjectExpertise} Your exercises are known for being engaging, clear, and precisely aligned with curriculum standards. ${learningObjectives}

    # CURRICULUM CONTEXT
    The following content is from official curriculum documents and should guide your exercise creation:

    ## SUBJECT CONTEXT
    ${userRequest.subject ? `This exercise is for ${userRequest.subject}${userRequest.parentSubject ? ` which is part of the ${userRequest.parentSubject} curriculum` : ''}.` : ''}

    ${userRequest.subjectData && userRequest.subjectData.length > 0 ? `## SUBJECT STRUCTURE
    ${userRequest.subjectData.map(subject =>
      `- ${subject.label}:
        ${subject.items.map(item => `  * ${item}`).join('\n        ')}`
    ).join('\n    ')}

    IMPORTANT: You must create questions that cover ALL the subjects listed above. Ensure a balanced distribution of questions across all subjects, with at least one question from each subject category.` : ''}

    ${examinationFormatText ? `## EXAMINATION FORMAT GUIDELINES
    The following guidelines define the examination format for this school. Adhere to them strictly:
    ${examinationFormatText}
    ` : ''}

    ## EXERCISE REQUIREMENTS
    ${metadataString}

    ## CURRICULUM CONTENT
    ${contextContent}

    # EXERCISE SPECIFICATIONS
    Create a ${userRequest.difficulty} level ${exerciseTypeStr} exercise about "${userRequest.topic}" for ${userRequest.grade} students.

    ## CORE REQUIREMENTS
    1. Strictly follow the curriculum guidelines provided above
    2. Be age-appropriate for ${userRequest.grade} students
    3. Include clear, concise instructions for students
    4. Provide complete answer keys with detailed explanations
    5. Align precisely with educational standards for ${userRequest.topic}
    6. Structure with a clear title, instructions, questions, and answers
    7. Ensure questions progressively increase in difficulty (if multiple questions)
    8. Include exactly ${userRequest.totalQuestions} questions
    9. Ensure questions test conceptual understanding, not just memorization
    10. Include real-world applications where appropriate
    ${userRequest.includeImages ? '11. Create detailed image prompts that include ALL measurements and spatial relationships from the question' : ''}
    ${userRequest.includeImages ? '12. Image prompts must be specific enough to generate accurate diagrams (e.g., "A rectangular garden 12m long and 8m wide surrounded by a path of uniform width")' : ''}
    ${userRequest.includeImages ? '13. Never use vague image prompts like "A rectangular garden surrounded by a path" - always include all measurements' : ''}
    ${userRequest.includeImages ? '14. For questions involving vehicles and speed/time/distance, include instructions to show speedometers, clocks, and distance markers' : ''}

    ${topicSpecificInstructions}

    ${exerciseTypeInstructions}

    # COGNITIVE LEVELS
    Ensure your questions span different cognitive levels:
    1. Knowledge/Recall: Basic recall of facts, terms, concepts (20% of questions)
    2. Understanding/Comprehension: Demonstrating understanding of concepts (30% of questions)
    3. Application: Applying knowledge to new situations (30% of questions)
    4. Analysis/Evaluation: Breaking down information, making judgments (20% of questions)

    # OUTPUT FORMAT
    The response must be valid JSON with the following structure:
    {
        "result": [
            {
                "type": "question type", // Use specific types: "multiple_choice", "single_choice", "fill_blank", "creative_writing", "open_ended", "matching"
                "difficulty": "question difficulty", // REQUIRED: Must be one of: "Easy", "Medium", "Advanced"
                "content": "question content in HTML format",
                "options": ["option1", "option2", "option3", "option4"], // Both multiple choice and single choice questions must have exactly 4 options; For multiple choice: 2-3 correct answers
                "answer": ["correct answer option(s)"],
                "imagePrompt": "detailed description including ALL measurements and spatial relationships from the question (e.g., 'A rectangular garden 12m long and 8m wide surrounded by a path of uniform width')",
                "explain": "<p>Step-by-step explanation in HTML format</p>",
                "subject": "Parent subject category" // REQUIRED: Specify which parent subject this question belongs to (e.g., "Whole Numbers", "Fractions", etc.)
            }
        ]
    }

    # FORMATTING REQUIREMENTS
    1. Content and explanations MUST use HTML formatting (<p>, <ul>, <ol>, etc.)
    2. Mathematical formulas MUST use MathML format: <math xmlns="http://www.w3.org/1998/Math/MathML">...</math>
    3. Do NOT include numbering or prefixes like "1)" or "Question 1" in the question content
    4. Do NOT include letter labels (A, B, C, D) in the options array - just the raw text
    5. Explanations MUST follow a clear step-by-step format: "<p>Step 1: ... </p><p>Step 2: ... </p>"
    6. Ensure all HTML is properly formatted and closed
    7. The final output MUST be valid JSON that can be parsed without errors
    8. The explanations must be short, concise, and to the point
    9. The explanations must be readable by students at the ${userRequest.grade} level
    10. The explanations must use tailwindcss classes for styling (e.g., text-blue-500, font-bold)
    11. Both multiple choice and single choice questions MUST have EXACTLY 4 options
    12. Multiple choice questions MUST have at least 2 and at most 3 correct answers

    # CRITICAL REQUIREMENTS
    - Ensure exercises precisely match curriculum requirements and learning objectives
    - Questions must be clear, unambiguous, and have definitive correct answers
    - Content must be factually accurate and educationally sound
    - Avoid cultural bias and ensure content is inclusive and accessible
    - Ensure appropriate difficulty level for ${userRequest.grade} students
    - IMPORTANT: Each question MUST include a "subject" field that specifies which parent subject category it belongs to
    - When multiple subjects are provided, ensure a balanced distribution with at least one question from each subject

    ${userRequest.includeImages ? `# IMAGE GENERATION REQUIREMENTS
    ## General Image Requirements
    - Every image prompt must include ALL measurements and spatial relationships mentioned in the question
    - All numerical values in the question must be represented visually in the image
    - Image prompts must be detailed and specific, not vague
    - Include clear instructions on what elements must be shown in the image

    ## Question Type-Specific Image Requirements

    ### For Vehicle Journey Questions
    - If the question mentions a vehicle (car, train, bus, etc.) and involves speed, time, or distance:
      * Instruct to include a clear visual of the vehicle
      * If speed is mentioned, instruct to include a speedometer/gauge showing the exact speed value
      * If time is mentioned, instruct to include a clock or timer showing the exact duration
      * If distance is mentioned, instruct to include a distance marker or route map with the exact distance
      * Instruct to position these elements in a logical arrangement that helps understand their relationship

    ### For Geometry Questions
    - For shapes, instruct to maintain precise proportions matching the exact measurements
    - For angles, instruct to show accurate angle measurements
    - For area/perimeter problems, instruct to clearly label all dimensions

    ## Examples of Good Image Prompts
    - "Show a car on a road with a speedometer displaying 240 km/h, a clock showing 3 hours, and a distance marker showing 240 km"
    - "Draw a rectangle with length 8m and width 4m, with all sides clearly labeled"
    - "Draw a triangle with angles 30°, 60°, and 90°, with all angles clearly labeled"
    - "Show a circle with radius 5cm, with the radius clearly marked and labeled"` : ''}
    `.trim();

    return prompt;
  }

  /**
   * Builds a user prompt for generating educational exercises
   * @param userRequest User's specific request parameters
   * @param metaString Formatted metadata about the exercise requirements
   * @param narrativeStructure Optional school-specific narrative structure
   * @returns A complete user prompt for the AI
   */
  buildUserPrompt(userRequest: UserRequest, metaString:string, narrativeStructure?: string): string {
    const exerciseTypeStr = userRequest.exerciseType.join(', ');

    // Use factories to create strategies
    const topicStrategy = TopicInstructionsFactory.createStrategy(userRequest.topic);
    const exerciseTypeStrategy = ExerciseTypeFactory.createStrategy(userRequest.exerciseType);

    // Get instructions from strategies
    const topicSpecificInstructions = topicStrategy.generateInstructions();
    const exerciseTypeInstructions = exerciseTypeStrategy.generateInstructions();

    // Build subject context based on the subject hierarchy:
    // topic: Main subject area (e.g., "Mathematics")
    // parentSubject: Chapter/unit (e.g., "Fractions")
    // subject: Specific lesson content (e.g., "Dividing a whole number by a proper fraction")
    let subjectContext = '';
    if (userRequest.subject) {
      // If we have specific lesson content
      subjectContext = userRequest.parentSubject && userRequest.topic
        ? `in ${userRequest.subject} (from the ${userRequest.parentSubject} chapter of ${userRequest.topic})`
        : userRequest.parentSubject
          ? `in ${userRequest.subject} (from the ${userRequest.parentSubject} chapter)`
          : userRequest.topic
            ? `in ${userRequest.subject} (part of ${userRequest.topic})`
            : `in ${userRequest.subject}`;
    } else if (userRequest.parentSubject) {
      // If we have chapter/unit but no specific lesson
      subjectContext = userRequest.topic
        ? `in the ${userRequest.parentSubject} chapter of ${userRequest.topic}`
        : `in the ${userRequest.parentSubject} chapter`;
    }

    // Create a learning objectives statement based on the subject hierarchy
    const learningObjectives = userRequest.subject
      ? `The exercise should focus on developing students' understanding of ${userRequest.subject} concepts and skills.`
      : userRequest.parentSubject
        ? `The exercise should focus on developing students' understanding of key concepts within ${userRequest.parentSubject}.`
        : `The exercise should focus on developing students' understanding of key ${userRequest.topic} concepts appropriate for ${userRequest.grade} level.`;

    const prompt = `
    # EXERCISE REQUEST
    I need a high-quality ${userRequest.difficulty} level ${exerciseTypeStr} exercise about "${userRequest.topic}" ${subjectContext} for ${userRequest.grade} students. ${learningObjectives}

    # SUBJECT REQUIREMENTS
    ${userRequest.subject ? `This exercise must align with ${userRequest.subject} curriculum standards${userRequest.parentSubject ? ` within the broader ${userRequest.parentSubject} framework` : ''}.` : ''}

    ${userRequest.subjectData && userRequest.subjectData.length > 0 ? `# SUBJECT STRUCTURE
    ${userRequest.subjectData.map(subject =>
      `- ${subject.label}:
        ${subject.items.map(item => `  * ${item}`).join('\n        ')}`
    ).join('\n    ')}

    IMPORTANT: You must create questions that cover ALL the subjects listed above. Ensure a balanced distribution of questions across all subjects, with at least one question from each subject category.` : ''}

    # REQUIREMENTS
    ${metaString}
    - Total questions: ${userRequest.totalQuestions}
    - The exercise should be engaging, clear, and educationally sound
    - Questions should progressively increase in difficulty
    - Include detailed explanations for all answers
    - Ensure questions test conceptual understanding, not just memorization
    - Include real-world applications where appropriate
    - Ensure questions are age-appropriate for ${userRequest.grade} students
    - Ensure questions have clear, unambiguous answers
    ${userRequest.includeImages ? '- Include detailed image prompts with ALL measurements and spatial relationships from the question' : ''}
    ${userRequest.includeImages ? '- Image prompts must be specific (e.g., "A rectangular garden 12m long and 8m wide surrounded by a path of uniform width")' : ''}
    ${userRequest.includeImages ? '- Never use vague descriptions - always include all relevant measurements and details' : ''}
    ${userRequest.includeImages ? '- For vehicle journey questions, include instructions to show speedometers, clocks, and distance markers' : ''}

    # COGNITIVE LEVELS
    Include questions at different cognitive levels:
    - Knowledge/Recall: Basic recall of facts, terms, concepts
    - Understanding/Comprehension: Demonstrating understanding of concepts
    - Application: Applying knowledge to new situations
    - Analysis/Evaluation: Breaking down information, making judgments

    ${narrativeStructure ? `# SCHOOL-SPECIFIC NARRATIVE STRUCTURE
    The following narrative structure should be used when creating questions. This structure reflects the examination format used by the school:

    ${narrativeStructure}

    IMPORTANT: Ensure that questions follow this narrative structure to maintain consistency with the school's examination format.
    ` : ''}

    ${topicSpecificInstructions.split('##').join('#')}

    ${exerciseTypeInstructions.split('##').join('#').split('###').join('##')}

    ${userRequest.includeImages ? `# IMAGE PROMPT REQUIREMENTS

    ## General Requirements
    - Every image prompt must include ALL measurements and spatial relationships mentioned in the question
    - All numerical values in the question must be represented visually in the image
    - Image prompts must be detailed and specific, not vague

    ## Question Type-Specific Requirements

    ### For Vehicle Journey Questions
    - If the question mentions a vehicle (car, train, bus, etc.) and involves speed, time, or distance:
      * Instruct to include a clear visual of the vehicle
      * If speed is mentioned, instruct to include a speedometer/gauge showing the exact speed value
      * If time is mentioned, instruct to include a clock or timer showing the exact duration
      * If distance is mentioned, instruct to include a distance marker or route map with the exact distance

    ### For Geometry Questions
    - For shapes, instruct to maintain precise proportions matching the exact measurements
    - For angles, instruct to show accurate angle measurements
    - For area/perimeter problems, instruct to clearly label all dimensions

    ## Examples of Good Image Prompts
    - "Show a car on a road with a speedometer displaying 240 km/h, a clock showing 3 hours, and a distance marker showing 240 km"
    - "Draw a rectangle with length 8m and width 4m, with all sides clearly labeled"
    - "Draw a triangle with angles 30°, 60°, and 90°, with all angles clearly labeled"
    - "Show a circle with radius 5cm, with the radius clearly marked and labeled"` : ''}

    # OUTPUT FORMAT
    Please provide the exercise in valid JSON format with this structure:
    {
        "result": [
            {
                "type": "question type", // Use specific types: "multiple_choice", "single_choice", "fill_blank", "creative_writing", "open_ended", "matching"
                "difficulty": "question difficulty", // REQUIRED: Must be one of: "Easy", "Medium", "Advanced"
                "content": "question content in HTML format",
                "options": ["option1", "option2", "option3", "option4"], // Both multiple choice and single choice questions must have exactly 4 options; For multiple choice: 2-3 correct answers
                "answer": ["correct answer option(s)"],
                "imagePrompt": "detailed description including ALL measurements and spatial relationships from the question (e.g., 'A rectangular garden 12m long and 8m wide surrounded by a path of uniform width')",
                "explain": "<p>Step-by-step explanation in HTML format</p>",
                "subject": "Parent subject category" // REQUIRED: Specify which parent subject this question belongs to (e.g., "Whole Numbers", "Fractions", etc.)
            }
        ]
    }

    # FORMATTING NOTES
    - Content and explanations MUST use HTML formatting (<p>, <ul>, <ol>, etc.)
    - Mathematical formulas MUST use MathML format: <math xmlns="http://www.w3.org/1998/Math/MathML">...</math>
    - Do NOT include numbering or prefixes like "1)" or "Question 1" in the question content
    - Do NOT include letter labels (A, B, C, D) in the options array - just the raw text
    - Explanations MUST follow a clear step-by-step format: "<p>Step 1: ... </p><p>Step 2: ... </p>"
    - Ensure all HTML is properly formatted and closed
    - The final output MUST be valid JSON that can be parsed without errors
    - The explanations must be short, concise, and to the point
    - The explanations must be readable by students at the ${userRequest.grade} level
    - The explanations must use tailwindcss classes for styling (e.g., text-blue-500, font-bold)
    - Both multiple choice and single choice questions MUST have EXACTLY 4 options
    - Multiple choice questions MUST have at least 2 and at most 3 correct answers
    `.trim();

    return prompt;
  }

  /**
   * Generate an SVG prompt based on the question content and subject
   * @param imagePrompt The question content to illustrate
   * @param topic Optional subject/topic for specialized instructions
   * @returns A complete prompt for SVG generation
   */
  enhanceSvgPrompt(imagePrompt: string, topic?: string): string {
    // Create the context and set the appropriate strategy based on the topic
    const context = new SvgPromptContext();
    const strategy = SvgStrategyFactory.createStrategy(topic);
    context.setStrategy(strategy);

    // Extract key elements from the prompt for better visualization
    const hasSpeed = imagePrompt.toLowerCase().includes('km/h') ||
                     imagePrompt.toLowerCase().includes('speed') ||
                     imagePrompt.toLowerCase().includes('velocity');

    const hasTime = imagePrompt.toLowerCase().includes('hour') ||
                    imagePrompt.toLowerCase().includes('minute') ||
                    imagePrompt.toLowerCase().includes('second') ||
                    imagePrompt.toLowerCase().includes('duration');

    const hasDistance = imagePrompt.toLowerCase().includes('km') ||
                        imagePrompt.toLowerCase().includes('meter') ||
                        imagePrompt.toLowerCase().includes('distance') ||
                        imagePrompt.toLowerCase().includes('journey');

    const hasVehicle = imagePrompt.toLowerCase().includes('car') ||
                       imagePrompt.toLowerCase().includes('train') ||
                       imagePrompt.toLowerCase().includes('bus') ||
                       imagePrompt.toLowerCase().includes('vehicle');

    // Add specific visualization instructions based on content
    let enhancedPrompt = imagePrompt;
    let additionalInstructions = '';

    if (hasVehicle && (hasSpeed || hasTime || hasDistance)) {
      additionalInstructions += `

# VEHICLE JOURNEY SPECIFIC INSTRUCTIONS
- This question involves a vehicle journey that requires specific visual elements
- Include a clear visual of the vehicle mentioned (car, train, bus, etc.)
`;

      if (hasSpeed) {
        additionalInstructions += `- Show a speedometer/gauge displaying the exact speed value (${imagePrompt.match(/\d+\s*km\/h|\d+\s*kilometers per hour|\d+\s*miles per hour|\d+\s*mph/i) || 'as mentioned in the question'})
- Make sure the speedometer is clearly readable with the exact speed value marked
`;
      }

      if (hasTime) {
        additionalInstructions += `- Include a clock or timer showing the exact duration (${imagePrompt.match(/\d+\s*hours?|\d+\s*minutes?|\d+\s*seconds?/i) || 'as mentioned in the question'})
- Ensure the time display is clearly visible and accurate
`;
      }

      if (hasDistance) {
        additionalInstructions += `- Show a distance marker or route map with the exact distance (${imagePrompt.match(/\d+\s*km|\d+\s*kilometers|\d+\s*miles|\d+\s*m|\d+\s*meters/i) || 'as mentioned in the question'})
- Make the distance measurement clearly visible on the diagram
`;
      }

      additionalInstructions += `- Position these elements (vehicle, ${hasSpeed ? 'speedometer, ' : ''}${hasTime ? 'clock, ' : ''}${hasDistance ? 'distance marker, ' : ''}) in a logical arrangement
- Ensure all elements are clearly labeled and visually connected to show their relationship
- Use a layout that helps understand the relationship between speed, time, and distance
`;
    }

    // Add the additional instructions to the prompt
    if (additionalInstructions) {
      enhancedPrompt += "\n\n" + additionalInstructions;
    }

    // Generate the complete prompt using the strategy pattern with enhanced content
    return context.generatePrompt(enhancedPrompt);
  }

  /**
   * Enhance an image prompt with detailed specifications for educational diagrams
   * @param imagePrompt The original image prompt from the question
   * @returns A complete prompt for image generation with detailed specifications
   */
  enhanceImagePrompt(imagePrompt: string): string {
    // Extract key elements from the prompt for better visualization
    const hasSpeed = imagePrompt.toLowerCase().includes('km/h') ||
                     imagePrompt.toLowerCase().includes('speed') ||
                     imagePrompt.toLowerCase().includes('velocity');

    const hasTime = imagePrompt.toLowerCase().includes('hour') ||
                    imagePrompt.toLowerCase().includes('minute') ||
                    imagePrompt.toLowerCase().includes('second') ||
                    imagePrompt.toLowerCase().includes('duration');

    const hasDistance = imagePrompt.toLowerCase().includes('km') ||
                        imagePrompt.toLowerCase().includes('meter') ||
                        imagePrompt.toLowerCase().includes('distance') ||
                        imagePrompt.toLowerCase().includes('journey');

    const hasVehicle = imagePrompt.toLowerCase().includes('car') ||
                       imagePrompt.toLowerCase().includes('train') ||
                       imagePrompt.toLowerCase().includes('bus') ||
                       imagePrompt.toLowerCase().includes('vehicle');

    // Build specific visualization instructions based on content
    let specificInstructions = '';

    if (hasVehicle && (hasSpeed || hasTime || hasDistance)) {
      specificInstructions += `
    VEHICLE JOURNEY VISUALIZATION REQUIREMENTS:
    - Include a clear visual of the vehicle mentioned (car, train, bus, etc.)
    - If speed is mentioned, include a speedometer/gauge showing the exact speed value
    - If time is mentioned, include a clock or timer showing the duration
    - If distance is mentioned, include a distance marker or route map with the exact distance
    - For problems involving speed, time, and distance, show all three elements clearly
    - Position these elements in a logical arrangement that helps understand their relationship
    `;
    }

    return `Create a precise educational diagram for examination purposes: ${imagePrompt}

    CRITICAL DIAGRAM SPECIFICATIONS:
    - Use EXACTLY ONE visual representation per question
    - Create ONLY clean black lines on plain white background
    - Maintain PRECISE mathematical proportions matching the exact measurements in the question
    - Ensure all dimensions are proportionally accurate (e.g., 12m should be twice as long as 6m)
    - Draw ruler-straight lines with perfect geometric accuracy
    - Do NOT include any calculations or answers in the diagram
    - Do NOT add any information not explicitly stated in the question
    - For pie charts: create only ONE circle with clean divisions
    - For graphs: include clear axis labels, units, and appropriate scale
    - For geometric shapes: maintain precise angles and proportions
    - ALWAYS include visual representations of ALL key numerical values mentioned in the question
    - For questions with formulas, clearly show the formula with all variables labeled
    - For questions with units (km/h, meters, etc.), visually represent these units

    ESSENTIAL LABEL REQUIREMENTS:
    - Position all labels with a minimum 5px clearance from any line or object
    - Prevent ANY overlap between labels and other elements
    - Use thin connector lines to position labels away from crowded areas
    - For angles, place measurements at least 10px away from vertices
    - Center segment labels above lines with at least 8px clearance
    - Use uniform, legible sans-serif font for all text
    - Ensure all text is perfectly horizontal (no rotated text)
    - For tables, ensure cell content has sufficient padding and no overflow
    - For measurements, include units (e.g., "5 cm" not just "5")
    - For coordinates, use standard notation (e.g., "(3, 4)")
    - For speeds, always include the unit (e.g., "240 km/h")
    - For time durations, clearly label with appropriate units (e.g., "3 hours")

    ${specificInstructions}

    PROHIBITED ELEMENTS:
    - NO overlapping objects or labels under any circumstances
    - NO hand-drawn or sketchy appearance
    - NO unnecessary elements not mentioned in the question
    - NO decorative patterns, textures, or shading
    - NO degree measurements unless explicitly requested
    - NO multiple representations of the same concept
    - NO crowding of elements in any area of the diagram
    - NO answers or solution hints visible in the diagram
    - NO color (use only black and white)
    - NO 3D effects unless specifically required
    - NO missing key elements mentioned in the question (ALL numerical values must be visualized)

    VERIFICATION CHECKLIST (must satisfy ALL):
    1. Contains exact elements mentioned in question, nothing more
    2. All measurements are proportionally accurate
    3. No overlapping elements anywhere
    4. No answers or hints visible
    5. Single, clean representation
    6. All labels are clearly visible and not obscured
    7. All text is horizontal and legible
    8. All lines are straight and precise
    9. All angles are accurate
    10. All units are included with measurements
    11. All key numerical values from the question are visually represented

    EXAMPLES OF GOOD VISUALIZATIONS:
    - For "A car travels 240 km in 3 hours": Show a car on a road with a speedometer showing speed, a clock showing 3 hours, and a distance marker showing 240 km
    - For "A rectangle with length 8m and width 4m": Show a precisely drawn rectangle with sides labeled 8m and 4m
    - For "A triangle with angles 30°, 60°, and 90°": Show a triangle with all angles clearly labeled
    - For "A circle with radius 5cm": Show a circle with the radius clearly marked and labeled

    Return only an HTML img tag with the src attribute containing the direct image URL.
    Example: <img src="https://example.com/image.png" alt="Educational diagram">

    The final image must be identical in style to official examination materials - clean, precise, and professionally rendered.`
  }

  /**
   * Builds a prompt for expanding short queries (<10 chars)
   * @param query The original query string
   * @param category Optional category for context
   * @param userRequest Optional user request for additional context
   * @returns A prompt string for query expansion
   */
  buildShortQueryExpansionPrompt(query: string, category?: string, userRequest?: UserRequest): string {
    // Construct the core prompt message
    let promptMessage = `
    Expand the following SHORT search query to improve semantic vector search results within an educational content system. Short queries require careful expansion to accurately capture potential user intent.

    **Original Query:** "${query}"
  `;

    // Conditionally add the category if it exists
    if (category) {
      promptMessage += `**Category:** "${category}"\n`;
    }

    // Add grade level if available
    if (userRequest?.grade) {
      promptMessage += `**Grade Level:** "${userRequest.grade}"\n`;
    }

    // Add subject hierarchy information if available
    // Note: The naming is counterintuitive but maintained for backward compatibility:
    // - topic: Main subject area (e.g., "Mathematics")
    // - parentSubject: Chapter/unit (e.g., "Fractions")
    // - subject: Specific lesson content (e.g., "Dividing a whole number by a proper fraction")

    if (userRequest?.topic) {
      promptMessage += `**Main Subject Area:** "${userRequest.topic}"\n`;
    }

    if (userRequest?.parentSubject) {
      promptMessage += `**Chapter/Unit:** "${userRequest.parentSubject}"\n`;
    }

    if (userRequest?.subject) {
      promptMessage += `**Specific Lesson:** "${userRequest.subject}"\n`;
    }

    // Add the expansion guidelines
    promptMessage += `
      **Expansion Guidelines:**
      Generate a list of relevant keywords and phrases based on the short original query. Since the query is brief, focus on:
      1.  **Inferring Intent:** Carefully determine the likely core concept or goal behind the short query.
      2.  **Broadening Scope (Carefully):** Include relevant synonyms, related concepts, and alternative phrasings.
      3.  **Contextualization:** Leverage the educational context (e.g., common topics, student language, curriculum structure) and the provided category (if any) to guide expansion.
      4.  **Content Aspects:** Consider terms related to educational content types or formats (e.g., 'definition', 'example', 'quiz', 'video lesson') that might be relevant.
      5.  **Specificity vs. Breadth:** Balance adding related terms with maintaining focus on the most probable intent. Avoid overly broad or tangential expansions.
      6.  **Educational Standards:** Consider relevant curriculum standards and learning objectives that might be associated with the query.
      7.  **Age-Appropriate Terms:** Use vocabulary appropriate for the grade level if specified.
      8.  **Common Misconceptions:** Include terms related to common misconceptions or challenges students face with this topic.

      **Output Format:**
      Provide ONLY a comma-separated list or a newline-separated list of the most relevant keywords and phrases for the vector search. Focus on quality and relevance over quantity. Aim for 10-15 highly relevant terms.
    `;

    // Trim whitespace and return
    return promptMessage.trim();
  }

  /**
   * Builds a prompt for expanding normal-length queries
   * @param query The original query string
   * @param category Optional category for context
   * @param userRequest Optional user request for additional context
   * @returns A prompt string for query expansion
   */
  buildNormalQueryExpansionPrompt(query: string, category?: string, userRequest?: UserRequest): string {
    // Construct the core prompt message
    let promptMessage = `
    Expand the following search query to improve semantic vector search results within an educational content system. The goal is to capture the core intent and find relevant materials even if they don't use the exact original phrasing.

    **Original Query:** "${query}"
`;

    // Conditionally add the category if it exists
    if (category) {
      promptMessage += `**Category:** "${category}"\n`;
    }

    // Add grade level if available
    if (userRequest?.grade) {
      promptMessage += `**Grade Level:** "${userRequest.grade}"\n`;
    }

    // Add subject hierarchy information if available
    // Note: The naming is counterintuitive but maintained for backward compatibility:
    // - topic: Main subject area (e.g., "Mathematics")
    // - parentSubject: Chapter/unit (e.g., "Fractions")
    // - subject: Specific lesson content (e.g., "Dividing a whole number by a proper fraction")

    if (userRequest?.topic) {
      promptMessage += `**Main Subject Area:** "${userRequest.topic}"\n`;
    }

    if (userRequest?.parentSubject) {
      promptMessage += `**Chapter/Unit:** "${userRequest.parentSubject}"\n`;
    }

    if (userRequest?.subject) {
      promptMessage += `**Specific Lesson:** "${userRequest.subject}"\n`;
    }

    // Add difficulty level if available
    if (userRequest?.difficulty) {
      promptMessage += `**Difficulty Level:** "${userRequest.difficulty}"\n`;
    }

    // Add the expansion guidelines
    promptMessage += `
    **Expansion Guidelines:**
    Generate a list of relevant keywords and phrases based on the original query, considering the following:
    1.  **Core Intent:** Identify the main concept or question.
    2.  **Synonyms & Related Terms:** Include alternative words and closely related concepts.
    3.  **Alternative Phrasings:** Capture different ways the same idea might be expressed or searched for.
    4.  **Educational Context:** Incorporate relevant educational terminology, concepts, and consider student/educator perspectives.
    5.  **Content Aspects:** Include terms related to relevant text content, metadata, or educational formats (e.g., 'worksheet', 'multiple choice', 'diagram').
    6.  **Relevance:** Ensure all expanded terms directly relate to the original query's intent.
    7.  **Clarity:** Use clear language; avoid unnecessary jargon.
    8.  **Curriculum Alignment:** Include terms that align with educational standards and curriculum frameworks.
    9.  **Learning Objectives:** Consider what learning objectives might be associated with this query.
    10. **Assessment Terms:** Include terms related to how this concept might be assessed or tested.

    **Output Format:**
    Provide ONLY a comma-separated list or a newline-separated list of the most relevant keywords and phrases for the vector search. Aim for 15-20 highly relevant terms that would improve document retrieval.
  `;

    // Trim whitespace and return
    return promptMessage.trim();
  }
}
