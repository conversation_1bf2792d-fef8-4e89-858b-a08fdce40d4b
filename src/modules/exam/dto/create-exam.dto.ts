import {
  IsUUID,
  IsString,
  <PERSON><PERSON><PERSON>al,
  Is<PERSON>rray,
  ValidateNested,
  IsInt,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

class SelectedOptionDto {
  @IsString()
  key: string;

  @IsString()
  value: string;
}

class ExamQuestionDto {
  @IsString()
  type: string;

  @IsString()
  content: string;

  @IsArray()
  options: string[];

  @IsArray()
  answer: string[];

  @IsOptional()
  @IsString()
  image?: string;

  @IsOptional()
  @IsString()
  explain?: string;
}

export class CreateExamDto {
  @IsUUID()
  worksheetId: string;

  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SelectedOptionDto)
  selectedOptions: SelectedOptionDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExamQuestionDto)
  questions: ExamQuestionDto[];

  @ApiPropertyOptional({
    description: 'Default number of days until the exam assignment expires. Can be overridden during assignment.',
    example: 7,
    minimum: 1,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  defaultExpiresInDays?: number;

  @ApiPropertyOptional({
    description: 'Default number of attempts allowed for the exam. Can be overridden during assignment.',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  defaultTotalAttempts?: number;
}
