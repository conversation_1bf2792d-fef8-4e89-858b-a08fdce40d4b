import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'typeorm';
import BaseEntity from 'src/core/entities/base-entity';
import { ApiProperty } from '@nestjs/swagger';
import { Exam } from './exam.entity';
import { User } from 'src/modules/user/entities/user.entity';

export enum AssignmentStatus {
  ASSIGNED = 'assigned',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
}

@Entity('exam_assignments')
@Unique(['examId', 'studentId'])
export class ExamAssignment extends BaseEntity {
  @ApiProperty({
    description: 'Score of the exam assignment',
    example: 85,
  })
  @Column({ type: 'integer', default: 0 })
  score: number;

  @ApiProperty({
    description: 'Status of the exam assignment',
    enum: AssignmentStatus,
    example: AssignmentStatus.ASSIGNED,
  })
  @Column({
    type: 'enum',
    enum: AssignmentStatus,
    default: AssignmentStatus.ASSIGNED,
  })
  status: AssignmentStatus;

  @ApiProperty({
    description: 'Feedback for the exam assignment',
    example: 'Well done, but there are areas to improve.',
  })
  @Column({ length: 500, nullable: true })
  feedback?: string;

  @ApiProperty({
    description: 'When the exam was assigned to the student',
    example: '2024-01-15T10:30:00Z',
  })
  @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP' })
  assignedAt: Date;

  @ApiProperty({
    description: 'When the student first opened the exam',
    example: '2024-01-15T11:00:00Z',
    required: false,
  })
  @Column({ type: 'timestamp with time zone', nullable: true })
  startedAt?: Date;

  @ApiProperty({
    description: 'When the student submitted the exam',
    example: '2024-01-15T12:00:00Z',
    required: false,
  })
  @Column({ type: 'timestamp with time zone', nullable: true })
  completedAt?: Date;

  @ApiProperty({
    description: 'The date and time when the exam assignment expires',
    example: '2024-01-20T23:59:59Z',
    required: false,
  })
  @Column({ type: 'timestamp with time zone', nullable: true })
  expiresAt?: Date;

  @ApiProperty({
    description: 'The total number of attempts allowed for this assignment',
    example: 3,
    default: 1,
  })
  @Column({ type: 'integer', default: 1 })
  totalAttempts: number;

  @ApiProperty({
    description: 'Reference to the exam',
    type: () => Exam,
  })
  @ManyToOne(() => Exam, { nullable: false })
  @JoinColumn({ name: 'examId' })
  exam: Exam;

  @Column()
  examId: string;

  @ApiProperty({
    description: 'Reference to the student taking the exam',
    type: () => User,
  })
  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'studentId' })
  student: User;

  @Column()
  studentId: string;
}
