import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'typeorm';
import BaseEntity from 'src/core/entities/base-entity';
import { Exam } from './exam.entity';
import { User } from 'src/modules/user/entities/user.entity';

@Entity('exam_results')
export class ExamResult extends BaseEntity {
  @ManyToOne(() => Exam, { eager: false, nullable: false })
  @JoinColumn({ name: 'examId' })
  exam: Exam;

  @Column()
  examId: string;

  @ManyToOne(() => User, { eager: false, nullable: false })
  @JoinColumn({ name: 'studentId' })
  student: User;

  @Column()
  studentId: string;

  @Column({ type: 'jsonb' })
  answers: any;

  @Column({ type: 'jsonb' })
  detail: any;

  @Column()
  score: number;

  @Column()
  total: number;

  @Column({ type: 'timestamp with time zone', nullable: true })
  startedAt?: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  submittedAt: Date;

  @Column({ type: 'int', nullable: true })
  timeSpent?: number;
}
