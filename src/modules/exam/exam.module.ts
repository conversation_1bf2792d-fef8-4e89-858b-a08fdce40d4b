import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Exam } from './entities/exam.entity';
import { ExamQuestion } from './entities/exam-question.entity';
import { ExamResult } from './entities/exam-result.entity';
import { ExamAssignment } from './entities/exam-assignment.entity';
import { User } from '../user/entities/user.entity';
import { ExamResultService } from './exam-result.service';
import { ExamAssignmentService } from './exam-assignment.service';
import { ExamController } from './exam.controller';
import { ExamService } from './exam.service';
import { AuthModule } from '../auth/auth.module';
import { PermissionModule } from '../permission/permission.module';
import { UsageTrackingModule } from '../usage-tracking/usage-tracking.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Exam, ExamQuestion, ExamResult, ExamAssignment, User]),
    forwardRef(() => AuthModule),
    PermissionModule,
    UsageTrackingModule, // Add UsageTrackingModule to ensure UsageTrackingService is available
  ],
  controllers: [ExamController],
  providers: [ExamService, ExamResultService, ExamAssignmentService],
  exports: [ExamService, ExamAssignmentService],
})
export class ExamModule {}
