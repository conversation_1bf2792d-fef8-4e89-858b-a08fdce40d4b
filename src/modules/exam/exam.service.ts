import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { Exam, ExamStatus } from './entities/exam.entity';
import { ExamQuestion } from './entities/exam-question.entity';
import { CreateExamDto } from './dto/create-exam.dto';
import { SubmitExamDto } from './dto/submit-exam.dto';
import { ExamResultService } from './exam-result.service';
import { ExamResult } from './entities/exam-result.entity';
import { EUserRole } from '../user/dto/create-user.dto';
import { User } from '../user/entities/user.entity';
import { GetExamResponseDto, ExamStats, IExamResultDetail } from './dto/get-exam-response.dto';
import { GetStudentExamResultDto, QuestionResultDto } from './dto/get-student-exam-result.dto';
import { ExamAssignmentService } from './exam-assignment.service';
import { AssignmentStatus } from './entities/exam-assignment.entity';

@Injectable()
export class ExamService {
  private readonly logger = new Logger(ExamService.name);

  constructor(
    @InjectRepository(Exam)
    private readonly examRepository: Repository<Exam>,
    @InjectRepository(ExamResult)
    private readonly examResultRepository: Repository<ExamResult>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly examResultService: ExamResultService,
    private readonly examAssignmentService: ExamAssignmentService,
  ) {}

  async createExam(userId: string, dto: CreateExamDto, userSchoolId?: string): Promise<Exam> {
    const existing = await this.examRepository.findOne({
      where: {
        userId,
        worksheetId: dto.worksheetId,
        status: ExamStatus.IN_PROGRESS,
      },
    });
    if (existing) {
      throw new ForbiddenException(
        'You already have an unfinished exam for this worksheet.',
      );
    }

    // Log schoolId for debugging
    if (userSchoolId) {
      this.logger.log(`ExamService: Creating exam with schoolId: ${userSchoolId} from authenticated user`);
    } else {
      this.logger.warn(`ExamService: Creating exam without schoolId - user has no associated school`);
    }

    const exam = this.examRepository.create({
      userId,
      worksheetId: dto.worksheetId,
      title: dto.title,
      description: dto.description,
      selectedOptions: dto.selectedOptions,
      status: ExamStatus.IN_PROGRESS,
      questions: dto.questions.map((q, idx) => ({
        ...q,
        index: idx,
        userAnswer: [],
        isCorrect: null,
      })),
      // Set default assignment settings
      defaultExpiresInDays: dto.defaultExpiresInDays,
      defaultTotalAttempts: dto.defaultTotalAttempts,
      // Associate exam with user's school
      ...(userSchoolId && { schoolId: userSchoolId }),
    });
    await this.examRepository.save(exam);
    return exam;
  }

  async getExamById(
    userId: string,
    examId: string,
    role: EUserRole,
    userSchoolId?: string,
  ): Promise<GetExamResponseDto> {
    const exam = await this.examRepository.findOne({
      where: { id: examId },
    });
    if (!exam) throw new NotFoundException('Exam not found');
    
    // Check if user has access to this exam (same school for non-admin users)
    if (userSchoolId && exam.schoolId && userSchoolId !== exam.schoolId) {
      throw new NotFoundException('Exam not found'); // Don't reveal that it exists but user can't access it
    }

    // For students, check if they have an assignment for this exam
    if (role === EUserRole.STUDENT) {
      const assignment = await this.examAssignmentService.findAssignment(examId, userId);
      if (!assignment) {
        throw new NotFoundException('Exam not found'); // Don't reveal that it exists but student is not assigned
      }
      
      // Validate that student can access this exam (check expiration and attempts)
      await this.examAssignmentService.validateStudentCanStartAttempt(examId, userId);
      
      // Update assignment status to IN_PROGRESS when exam is first accessed
      if (assignment.status === AssignmentStatus.ASSIGNED) {
        await this.examAssignmentService.updateAssignmentStatus(
          examId,
          userId,
          AssignmentStatus.IN_PROGRESS,
        );
      }
    }

    // For teachers who own the exam, admins, and school managers, return detailed statistics and results
    const canViewDetails = (
      // Teachers who own the exam
      ((role === EUserRole.TEACHER || role === EUserRole.INDEPENDENT_TEACHER) && exam.userId === userId) ||
      // Admins can view all exams
      role === EUserRole.ADMIN ||
      // School managers can view exams from their school
      (role === EUserRole.SCHOOL_MANAGER && userSchoolId && exam.schoolId === userSchoolId)
    );

    if (canViewDetails) {
      // Fetch all results for this exam
      const examResults = await this.examResultRepository.find({
        where: { examId },
        relations: ['student'],
      });
      
      // Fetch all assignments for this exam
      const examAssignments = await this.examAssignmentService.getAssignmentsForExam(examId);
      
      // Calculate statistics
      const stats = await this.calculateExamStats(examResults, exam);

      // Format results with student details
      const results = await this.formatExamResults(examResults, exam);

      // Format assignments with student details
      const assignments = examAssignments.map(assignment => ({
        studentId: assignment.studentId,
        studentName: assignment.student.name || 'Unknown',
        studentEmail: assignment.student.email || '',
        status: assignment.status,
        assignedAt: assignment.assignedAt,
        startedAt: assignment.startedAt,
        completedAt: assignment.completedAt,
        score: assignment.status === AssignmentStatus.COMPLETED ? assignment.score : undefined,
        feedback: assignment.feedback,
      }));

      return {
        ...exam,
        stats,
        results,
        assignments,
      } as GetExamResponseDto;
    } else {
      // For students, return basic exam info with their result
      const examResult = await this.examResultRepository.findOne({
        where: { examId, studentId: userId },
      });
      
      // Return basic structure for students (no stats or other results)
      return {
        ...exam,
        stats: {
          totalSubmissions: 0,
          passRate: 0,
          averageScore: 0,
          highestScore: 0,
          lowestScore: 0,
          averageTimeSpent: 0,
        },
        results: [],
        assignments: [], // Empty assignments array for students
        userResult: examResult || null,
      } as any;
    }
  }

  async getStudentExamResult(
    examId: string,
    studentId: string,
    teacherId: string,
    teacherRole: EUserRole,
  ): Promise<GetStudentExamResultDto> {
    // Validate that the user is a teacher or independent teacher
    if (teacherRole !== EUserRole.TEACHER && teacherRole !== EUserRole.INDEPENDENT_TEACHER) {
      throw new ForbiddenException('Only teachers can access detailed student results');
    }

    // Check if exam exists and teacher owns it
    const exam = await this.examRepository.findOne({
      where: { id: examId },
    });
    if (!exam) {
      throw new NotFoundException('Exam not found');
    }

    // Verify teacher owns the exam
    if (exam.userId !== teacherId) {
      throw new ForbiddenException('You can only view results for exams you own');
    }

    // Check if student is assigned to this exam
    const assignment = await this.examAssignmentService.findAssignment(examId, studentId);
    if (!assignment) {
      throw new NotFoundException('Student is not assigned to this exam');
    }

    // Get the exam result for this student
    const examResult = await this.examResultRepository.findOne({
      where: { examId, studentId },
      relations: ['student'],
    });
    if (!examResult) {
      throw new NotFoundException('Student has not submitted this exam yet');
    }

    // Get student details
    const student = await this.userRepository.findOne({
      where: { id: studentId },
      select: ['id', 'name'],
    });
    if (!student) {
      throw new NotFoundException('Student not found');
    }

    // Calculate percentage and status
    const percentage = (examResult.score / examResult.total) * 100;
    const passingScore = this.getPassingScore(exam.selectedOptions);
    const status: 'passed' | 'failed' = percentage >= passingScore ? 'passed' : 'failed';

    // Format the detailed questions from the result detail
    const questions: QuestionResultDto[] = examResult.detail.map((detail: any) => ({
      questionIndex: detail.questionIndex,
      content: detail.content,
      type: detail.type,
      studentAnswer: detail.userAnswer,
      correctAnswer: detail.answer,
      explanation: detail.explain,
      isCorrect: detail.isCorrect,
      score: detail.isCorrect ? 1 : 0,
    }));

    return {
      studentId: examResult.studentId,
      studentName: student.name || 'Unknown',
      examId: exam.id,
      examTitle: exam.title,
      score: examResult.score,
      total: examResult.total,
      percentage: Math.round(percentage * 100) / 100, // Round to 2 decimal places
      status,
      timeSpent: examResult.timeSpent,
      submittedAt: examResult.submittedAt,
      questions,
    };
  }

  private async calculateExamStats(examResults: ExamResult[], exam: Exam): Promise<ExamStats> {
    if (examResults.length === 0) {
      return {
        totalSubmissions: 0,
        passRate: 0,
        averageScore: 0,
        highestScore: 0,
        lowestScore: 0,
        averageTimeSpent: 0,
      };
    }

    const totalSubmissions = examResults.length;
    const scores = examResults.map(result => result.score);
    const totals = examResults.map(result => result.total);

    // Calculate pass rate using custom passing score from selectedOptions
    const passingScore = this.getPassingScore(exam.selectedOptions);
    const passedCount = examResults.filter((result, index) => {
      const percentage = (result.score / result.total) * 100;
      return percentage >= passingScore;
    }).length;
    const passRate = (passedCount / totalSubmissions) * 100;

    // Calculate average score
    const totalScore = scores.reduce((sum, score) => sum + score, 0);
    const averageScore = totalScore / totalSubmissions;

    // Find highest and lowest scores
    const highestScore = Math.max(...scores);
    const lowestScore = Math.min(...scores);

    // Calculate average time spent (prefer actual timeSpent, fallback to calculated time)
    let averageTimeSpent = 0;
    const timeSpents = examResults.map(result => {
      // Use actual timeSpent if available, otherwise calculate from timestamps
      if (result.timeSpent !== null && result.timeSpent !== undefined) {
        return result.timeSpent;
      } else if (exam.createdAt) {
        const examStartTime = new Date(exam.createdAt).getTime();
        const submitTime = new Date(result.submittedAt).getTime();
        return (submitTime - examStartTime) / 1000; // Convert to seconds
      }
      return 0;
    });

    const validTimeSpents = timeSpents.filter(time => time > 0);
    if (validTimeSpents.length > 0) {
      const totalTimeSpent = validTimeSpents.reduce((sum, time) => sum + time, 0);
      averageTimeSpent = totalTimeSpent / validTimeSpents.length;
    }

    return {
      totalSubmissions,
      passRate: Math.round(passRate * 100) / 100, // Round to 2 decimal places
      averageScore: Math.round(averageScore * 100) / 100,
      highestScore,
      lowestScore,
      averageTimeSpent: Math.round(averageTimeSpent),
    };
  }

  private async formatExamResults(examResults: ExamResult[], exam: Exam): Promise<IExamResultDetail[]> {
    const results: IExamResultDetail[] = [];

    for (const result of examResults) {
      // Get student details
      const student = await this.userRepository.findOne({
        where: { id: result.studentId },
        select: ['id', 'name'],
      });

      if (student) {
        const percentage = (result.score / result.total) * 100;
        const passingScore = this.getPassingScore(exam.selectedOptions);
        const status: 'passed' | 'failed' = percentage >= passingScore ? 'passed' : 'failed';

        results.push({
          studentId: result.studentId,
          studentName: student.name || 'Unknown',
          score: result.score,
          total: result.total,
          correctAnswers: result.score,
          totalQuestions: result.total,
          status,
          submittedAt: result.submittedAt,
          timeSpent: result.timeSpent,
        });
      }
    }

    return results;
  }

  /**
   * Extract passing score from exam selectedOptions
   * @param selectedOptions Array of key-value pairs from exam
   * @returns Passing score percentage (defaults to 50 if not found or invalid)
   */
  private getPassingScore(selectedOptions?: any[]): number {
    const defaultPassingScore = 50;

    if (!selectedOptions || !Array.isArray(selectedOptions)) {
      return defaultPassingScore;
    }

    const passingScoreOption = selectedOptions.find(option => option.key === 'passingScore');

    if (!passingScoreOption || !passingScoreOption.value) {
      return defaultPassingScore;
    }

    const parsedScore = parseFloat(passingScoreOption.value);

    // Validate the parsed score (should be between 0 and 100)
    if (isNaN(parsedScore) || parsedScore < 0 || parsedScore > 100) {
      return defaultPassingScore;
    }

    return parsedScore;
  }

  async submitExam(
    userId: string,
    examId: string,
    dto: SubmitExamDto,
    userSchoolId?: string,
  ): Promise<any> {
    const exam = await this.examRepository.findOne({
      where: { id: examId },
    });
    if (!exam) throw new NotFoundException('Exam not found');

    // Check if user has access to this exam (same school for non-admin users)
    if (userSchoolId && exam.schoolId && userSchoolId !== exam.schoolId) {
      throw new NotFoundException('Exam not found'); // Don't reveal that it exists but user can't access it
    }

    // For students, check if they have an assignment for this exam
    const assignment = await this.examAssignmentService.findAssignment(examId, userId);
    if (!assignment) {
      throw new NotFoundException('Exam not found'); // Don't reveal that it exists but student is not assigned
    }

    // Check if assignment is already completed
    if (assignment.status === AssignmentStatus.COMPLETED) {
      throw new BadRequestException('You have already submitted this exam.');
    }

    // Validate attempt limits and expiration before allowing submission
    await this.examAssignmentService.validateStudentCanStartAttempt(examId, userId);

    const existedResult = await this.examResultRepository.findOne({
      where: { examId, studentId: userId },
    });
    if (existedResult)
      throw new BadRequestException('You have already submitted this exam.');
    if (exam.status !== ExamStatus.IN_PROGRESS) {
      throw new BadRequestException('Exam is already completed or invalid.');
    }
    const result = this.examResultService.calculateResult(exam, dto.answers);
    
    // Get the startedAt timestamp from the assignment (when they first accessed the exam)
    let startedAt = assignment.startedAt;
    
    // If no startedAt is recorded in assignment, use current time as fallback
    // This could happen if assignment was created before this feature was implemented
    if (!startedAt) {
      startedAt = new Date();
      this.logger.warn(`No startedAt timestamp found for assignment ${assignment.id}, using current time as fallback`);
    }

    const examResult = this.examResultRepository.create({
      examId,
      studentId: userId,
      answers: dto.answers,
      detail: result.detail,
      score: result.score,
      total: result.total,
      timeSpent: dto.timeSpent,
      startedAt: startedAt,
    });
    await this.examResultRepository.save(examResult);
    
    // Update assignment status to COMPLETED
    await this.examAssignmentService.updateAssignmentStatus(
      examId,
      userId,
      AssignmentStatus.COMPLETED,
    );
    
    return result;
  }

  async getExamsByWorksheetId(
    worksheetId: string,
  ): Promise<Exam[]> {
    return this.examRepository.find({
      where: [
        { worksheetId }
      ],
    });
  }

  async getExamsForUser(
    userId: string,
    pagination: { page: number; limit: number },
    role?: EUserRole,
  ): Promise<{ exams: any[]; total: number }> {
    const { page, limit } = pagination;
    const skip = (page - 1) * limit;

    // For students, get exams through assignments
    if (role === EUserRole.STUDENT) {
      const assignments = await this.examAssignmentService.getAssignmentsForStudent(userId);
      
      // Extract exam IDs from assignments
      const examIds = assignments.map(assignment => assignment.examId);
      
      if (examIds.length === 0) {
        return { exams: [], total: 0 };
      }
      
      // Get paginated exams
      const [exams, total] = await this.examRepository.findAndCount({
        where: examIds.map(id => ({ id })),
        take: limit,
        skip: skip,
        order: {
          createdAt: 'DESC',
        },
      });
      
      // Attach assignment status to each exam
      const examsWithStatus = exams.map(exam => {
        const assignment = assignments.find(a => a.examId === exam.id);
        return {
          ...exam,
          assignmentStatus: assignment?.status,
        } as any;
      });
      
      return { exams: examsWithStatus, total };
    }
    
    // For teachers and admins, get exams they created
    const [exams, total] = await this.examRepository.findAndCount({
      where: { userId },
      take: limit,
      skip: skip,
      order: {
        createdAt: 'DESC',
      },
    });

    // Add statistics to each exam for teachers/admins
    const examsWithStats = await Promise.all(
      exams.map(async (exam) => {
        // Get exam results for this specific exam
        const examResults = await this.examResultRepository.find({
          where: { examId: exam.id },
        });
        
        // Calculate statistics
        const stats = await this.calculateExamStats(examResults, exam);
        
        return {
          ...exam,
          stats,
          totalSubmissions: stats.totalSubmissions,
          averageScore: stats.averageScore,
        };
      })
    );

    return { exams: examsWithStats, total };
  }

  async getStudentExamSummaries(studentId: string): Promise<any[]> {
    // Get all exam results for this student
    const examResults = await this.examResultRepository.find({
      where: { studentId },
      relations: ['exam'],
      order: {
        submittedAt: 'DESC',
      },
    });

    // Transform the results into summary format
    const summaries = examResults.map(result => {
      const percentage = (result.score / result.total) * 100;
      const passingScore = this.getPassingScore(result.exam.selectedOptions);
      const status: 'passed' | 'failed' = percentage >= passingScore ? 'passed' : 'failed';

      // Extract subject from selectedOptions
      const subject = this.getSubjectFromOptions(result.exam.selectedOptions);

      return {
        examId: result.examId,
        examTitle: result.exam.title,
        subject,
        score: result.score,
        total: result.total,
        percentage: Math.round(percentage * 100) / 100, // Round to 2 decimal places
        status,
        submittedAt: result.submittedAt,
      };
    });

    return summaries;
  }

  /**
   * Extract subject from exam selectedOptions
   * @param selectedOptions Array of key-value pairs from exam
   * @returns Subject name (defaults to 'Mathematics' if not found)
   */
  private getSubjectFromOptions(selectedOptions?: any[]): string {
    const defaultSubject = 'Mathematics';

    if (!selectedOptions || !Array.isArray(selectedOptions)) {
      return defaultSubject;
    }

    const topicOption = selectedOptions.find(option => option.key === 'topic');

    if (!topicOption || !topicOption.value) {
      return defaultSubject;
    }

    // Capitalize first letter and return the subject
    const subject = topicOption.value;
    return subject.charAt(0).toUpperCase() + subject.slice(1);
  }
}
