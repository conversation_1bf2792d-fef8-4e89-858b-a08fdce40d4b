import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import BaseEntity from 'src/core/entities/base-entity';
import { ApiProperty } from '@nestjs/swagger';
import { School } from 'src/modules/school/entities/school.entity';
import { User } from 'src/modules/user/entities/user.entity';

export enum InvitationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
}

@Entity('invitations')
export class Invitation extends BaseEntity {
  @ApiProperty({
    description: 'Email address of the invitee',
    example: '<EMAIL>',
  })
  @Column()
  email: string;

  @ApiProperty({
    description: 'Role to be assigned to the invitee',
    example: 'TEACHER',
  })
  @Column()
  role: string;

  @ApiProperty({
    description: 'Current status of the invitation',
    enum: InvitationStatus,
    example: InvitationStatus.PENDING,
  })
  @Column({
    type: 'enum',
    enum: InvitationStatus,
    default: InvitationStatus.PENDING,
  })
  status: InvitationStatus;

  @ApiProperty({
    description: 'Unique token for invitation verification',
    example: 'abc123-def456-ghi789',
  })
  @Column({ unique: true })
  token: string;

  @ApiProperty({
    description: 'Expiration date of the invitation',
    example: '2024-01-15T10:30:00Z',
  })
  @Column({ type: 'timestamp with time zone' })
  expiresAt: Date;

  @ApiProperty({
    description: 'Reference to the school the invitation is for',
    type: () => School,
  })
  @ManyToOne(() => School, { nullable: false })
  @JoinColumn({ name: 'schoolId' })
  school: School;

  @Column()
  schoolId: string;

  @ApiProperty({
    description: 'ID of the user who sent the invitation',
    example: 'uuid-string',
    required: false,
  })
  @Column({ nullable: true })
  invitedBy?: string;

  @ApiProperty({
    description: 'Reference to the invited user (for new invitation flow)',
    type: () => User,
    required: false,
  })
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'userId' })
  user?: User;

  @Column({ nullable: true })
  userId?: string;
}
