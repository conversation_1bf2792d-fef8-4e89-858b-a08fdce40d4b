import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InvitationController } from './invitation.controller';
import { InvitationService } from './invitation.service';
import { SendInvitationWithExamDto } from './dto/send-invitation-with-exam.dto';
import { EUserRole } from '../user/dto/create-user.dto';

describe('InvitationController - Invite Student with Exam Assignment Integration', () => {
  let controller: InvitationController;
  let service: jest.Mocked<InvitationService>;

  const mockUser = {
    sub: 'user-123',
    role: EUserRole.TEACHER,
    schoolId: 'school-123',
  };

  const mockInvitationResult: any = {
    invitation: {
      id: 'invitation-123',
      email: '<EMAIL>',
      schoolId: 'school-123',
      role: EUserRole.STUDENT,
      token: 'test-token',
      status: 'pending',
    },
    assignments: [
      {
        id: 'assignment-123',
        examId: 'exam-123',
        studentId: 'student-123',
        status: 'assigned',
        score: 0,
      },
    ],
  };

  beforeEach(async () => {
    const mockInvitationService = {
      createInvitationWithExamAssignments: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [InvitationController],
      providers: [
        {
          provide: InvitationService,
          useValue: mockInvitationService,
        },
      ],
    }).compile();

    controller = module.get<InvitationController>(InvitationController);
    service = module.get(InvitationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('inviteStudentWithExamAssignment', () => {
    it('should successfully invite student with exam assignment', async () => {
      // Arrange
      const dto: SendInvitationWithExamDto = {
        email: '<EMAIL>',
        schoolId: 'school-123',
        role: EUserRole.STUDENT,
        examIds: ['exam-123'],
      };

      service.createInvitationWithExamAssignments.mockResolvedValueOnce(mockInvitationResult);

      // Act
      const result = await controller.inviteStudentWithExamAssignment(mockUser, dto);

      // Assert
      expect(result).toEqual(mockInvitationResult);
      expect(service.createInvitationWithExamAssignments).toHaveBeenCalledWith(
        dto.email,
        dto.schoolId,
        EUserRole.STUDENT,
        dto.examIds,
        mockUser.sub,
        mockUser,
      );
    });

    it('should enforce student role when other role is provided', async () => {
      // Arrange
      const dto: SendInvitationWithExamDto = {
        email: '<EMAIL>',
        schoolId: 'school-123',
        role: EUserRole.TEACHER, // This should be changed to STUDENT
        examIds: ['exam-123'],
      };

      service.createInvitationWithExamAssignments.mockResolvedValueOnce(mockInvitationResult);

      // Act
      const result = await controller.inviteStudentWithExamAssignment(mockUser, dto);

      // Assert
      expect(dto.role).toBe(EUserRole.STUDENT);
      expect(service.createInvitationWithExamAssignments).toHaveBeenCalledWith(
        dto.email,
        dto.schoolId,
        EUserRole.STUDENT,
        dto.examIds,
        mockUser.sub,
        mockUser,
      );
    });

    it('should throw BadRequestException when teacher tries to invite for different school', async () => {
      // Arrange
      const dto: SendInvitationWithExamDto = {
        email: '<EMAIL>',
        schoolId: 'different-school-123',
        role: EUserRole.STUDENT,
        examIds: ['exam-123'],
      };

      // Act & Assert
      await expect(
        controller.inviteStudentWithExamAssignment(mockUser, dto),
      ).rejects.toThrow(BadRequestException);

      expect(service.createInvitationWithExamAssignments).not.toHaveBeenCalled();
    });

    it('should allow admin to invite for any school', async () => {
      // Arrange
      const adminUser = { ...mockUser, role: EUserRole.ADMIN };
      const dto: SendInvitationWithExamDto = {
        email: '<EMAIL>',
        schoolId: 'any-school-123',
        role: EUserRole.STUDENT,
        examIds: ['exam-123'],
      };

      service.createInvitationWithExamAssignments.mockResolvedValueOnce(mockInvitationResult);

      // Act
      const result = await controller.inviteStudentWithExamAssignment(adminUser, dto);

      // Assert
      expect(result).toEqual(mockInvitationResult);
      expect(service.createInvitationWithExamAssignments).toHaveBeenCalled();
    });

    it('should allow school manager to invite for their own school', async () => {
      // Arrange
      const schoolManagerUser = { ...mockUser, role: EUserRole.SCHOOL_MANAGER };
      const dto: SendInvitationWithExamDto = {
        email: '<EMAIL>',
        schoolId: 'school-123',
        role: EUserRole.STUDENT,
        examIds: ['exam-123'],
      };

      service.createInvitationWithExamAssignments.mockResolvedValueOnce(mockInvitationResult);

      // Act
      const result = await controller.inviteStudentWithExamAssignment(schoolManagerUser, dto);

      // Assert
      expect(result).toEqual(mockInvitationResult);
      expect(service.createInvitationWithExamAssignments).toHaveBeenCalled();
    });

    it('should handle service errors gracefully', async () => {
      // Arrange
      const dto: SendInvitationWithExamDto = {
        email: '<EMAIL>',
        schoolId: 'school-123',
        role: EUserRole.STUDENT,
        examIds: ['non-existent-exam'],
      };

      service.createInvitationWithExamAssignments.mockRejectedValueOnce(
        new NotFoundException('Exams not found: non-existent-exam'),
      );

      // Act & Assert
      await expect(
        controller.inviteStudentWithExamAssignment(mockUser, dto),
      ).rejects.toThrow(NotFoundException);
    });

    it('should validate exam assignment permissions through service', async () => {
      // Arrange
      const dto: SendInvitationWithExamDto = {
        email: '<EMAIL>',
        schoolId: 'school-123',
        role: EUserRole.STUDENT,
        examIds: ['other-user-exam'],
      };

      service.createInvitationWithExamAssignments.mockRejectedValueOnce(
        new ForbiddenException('You can only assign your own exams'),
      );

      // Act & Assert
      await expect(
        controller.inviteStudentWithExamAssignment(mockUser, dto),
      ).rejects.toThrow(ForbiddenException);
    });
  });
}); 