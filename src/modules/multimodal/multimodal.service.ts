import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ModelConfigService } from '../ai/model-config.service';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { promisify } from 'util';
import { Document } from 'llamaindex';
import { exec } from 'child_process';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class MultimodalService {
  private readonly logger = new Logger(MultimodalService.name);
  private readonly apiKey: string;
  private readonly apiEndpoint: string;
  private readonly imageStoragePath: string;

  constructor(
    private configService: ConfigService,
    private modelConfigService: ModelConfigService
  ) {
    const apiKeyFromConfig = this.configService.get<string>('OPENROUTER_API_KEY');
    if (!apiKeyFromConfig) {
      this.logger.warn('OPENROUTER_API_KEY not found in configuration. Multimodal processing may not work correctly.');
    }
    this.apiKey = apiKeyFromConfig || 'dummy-key';
    this.apiEndpoint = 'https://openrouter.ai/api/v1/chat/completions';
    this.imageStoragePath = this.configService.get<string>('IMAGE_STORAGE_PATH') || path.join(process.cwd(), 'uploads', 'images');

    // Ensure the image storage directory exists
    if (!fs.existsSync(this.imageStoragePath)) {
      fs.mkdirSync(this.imageStoragePath, { recursive: true });
    }
  }

  /**
   * Process a PDF file with multimodal AI to extract both text and image content
   * @param fileBuffer The PDF file buffer
   * @param documentId The document ID
   * @returns An array of Document objects with text and image references
   */
  async processPdfWithMultimodalAI(fileBuffer: Buffer, documentId: string): Promise<Document[]> {
    try {
      this.logger.debug(`Starting multimodal processing of PDF with document ID: ${documentId}`);

      // Extract pages from PDF (both text and images)
      const pdfPages = await this.extractPdfPages(fileBuffer, documentId);
      this.logger.debug(`Extracted ${pdfPages.length} pages from PDF`);

      // Process each page with the multimodal model using map and Promise.all instead of for loop
      const processedDocuments = await Promise.all(
        pdfPages.map(async (page, i) => {
          this.logger.debug(`Processing page ${i + 1} with multimodal AI`);

          // Skip pages with no content
          if (!page.text && !page.imageUrl) {
            this.logger.debug(`Skipping page ${i + 1} - no content`);
            return null; // Return null for pages to skip
          }

          // Process with multimodal AI
          const enhancedContent = await this.processWithQwen(page.text, page.imageUrl);

          // Create a document with the enhanced content
          const metadata: any = {
            pageNumber: i + 1,
            documentId: documentId,
            hasImage: !!page.imageUrl,
            originalText: page.text,
          };

          if (page.imageUrl) {
            metadata.imageUrl = page.imageUrl;
          }

          return new Document({
            text: enhancedContent,
            id_: `${documentId}-page-${i + 1}`,
            metadata,
          });
        })
      );

      // Filter out null values (skipped pages)
      const filteredDocuments = processedDocuments.filter(doc => doc !== null);

      this.logger.debug(`Multimodal processing complete. Created ${filteredDocuments.length} documents`);
      return filteredDocuments;
    } catch (error) {
      this.logger.error(`Error in multimodal processing: ${error.message}`, error.stack);
      throw new Error(`Failed to process PDF with multimodal AI: ${error.message}`);
    }
  }

  /**
   * Extract pages from a PDF file, including both text and images
   * @param fileBuffer The PDF file buffer
   * @param documentId The document ID
   * @returns An array of page objects with text and image URLs
   * @private
   */
  private async extractPdfPages(fileBuffer: Buffer, documentId: string): Promise<Array<{ text: string; imageUrl: string | null }>> {
    try {
      // Create a temporary file to store the PDF
      const tempPdfPath = path.join(this.imageStoragePath, `${documentId}-temp.pdf`);
      await promisify(fs.writeFile)(tempPdfPath, fileBuffer);

      const execPromise = promisify(exec);
      const pages: Array<{ text: string; imageUrl: string | null }> = [];

      try {
        // Use pdftotext to extract text (requires poppler-utils)
        const { stdout: pdfInfo } = await execPromise(`pdfinfo ${tempPdfPath}`);

        // Extract page count from pdfinfo output
        const pageCountMatch = pdfInfo.match(/Pages:\s+(\d+)/);
        const pageCount = pageCountMatch ? parseInt(pageCountMatch[1], 10) : 0;

        this.logger.debug(`PDF has ${pageCount} pages`);

        // Process each page using Array.from and Promise.all instead of for loop
        const pagePromises = Array.from({ length: pageCount }, async (_, index) => {
          const i = index + 1; // Convert 0-based index to 1-based page number
          try {
            // Extract text using pdftotext
            const { stdout: pageText } = await execPromise(`pdftotext -f ${i} -l ${i} -layout ${tempPdfPath} -`);
            const text = pageText.trim();

            this.logger.debug(`Extracted text from page ${i}: ${text.substring(0, 100)}...`);

            // Generate image using pdftoppm
            let imageUrl: string | null = null;
            try {
              // Generate a unique filename
              const imageFilename = `${documentId}-page-${i}.png`;
              const imagePath = path.join(this.imageStoragePath, imageFilename);

              // Use pdftoppm to convert PDF page to image
              await execPromise(`pdftoppm -png -f ${i} -l ${i} -r 150 ${tempPdfPath} ${path.join(this.imageStoragePath, documentId)}-page`);

              // Check if the image was created
              const generatedImagePath = path.join(this.imageStoragePath, `${documentId}-page-${i}.png`);
              if (fs.existsSync(generatedImagePath)) {
                // Create a URL for the image
                imageUrl = `/api/files/images/${imageFilename}`;
                this.logger.debug(`Created image for page ${i}: ${imageUrl}`);
              }
            } catch (imageError) {
              this.logger.error(`Error creating image for page ${i}: ${imageError.message}`);
              // Continue without image if there's an error
            }

            return {
              text,
              imageUrl,
            };
          } catch (pageError) {
            this.logger.error(`Error processing page ${i}: ${pageError.message}`);
            // Return null for failed pages
            return null;
          }
        });

        // Wait for all page processing to complete and filter out null values
        const processedPages = (await Promise.all(pagePromises)).filter(page => page !== null);

        // Add all processed pages to the pages array
        pages.push(...processedPages);
      } catch (pdfError) {
        this.logger.error(`Error processing PDF: ${pdfError.message}`);
        // If we can't extract text with pdftotext, create a single page with empty text
        pages.push({
          text: 'PDF text extraction failed. Please try a different file format.',
          imageUrl: null,
        });
      }

      // Clean up the temporary PDF file
      try {
        await promisify(fs.unlink)(tempPdfPath);
      } catch (unlinkError) {
        this.logger.error(`Error removing temporary PDF file: ${unlinkError.message}`);
      }

      return pages;
    } catch (error) {
      this.logger.error(`Error extracting PDF pages: ${error.message}`, error.stack);
      throw new Error(`Failed to extract PDF pages: ${error.message}`);
    }
  }



  /**
   * Process text and image with the Qwen 2.5 VL model
   * @param text The text content
   * @param imageUrl The image URL (if any)
   * @returns Enhanced content from the multimodal model
   * @private
   */
  /**
   * Extract full text from a PDF file
   * @param fileBuffer The PDF file buffer
   * @returns A string containing all text from the PDF
   */
  async extractFullTextFromPdf(fileBuffer: Buffer): Promise<string> {
    let fullText = '';
    this.logger.debug('Starting full text extraction from PDF using extractPdfPages');

    try {
      const documentId = uuidv4(); // Generate a temporary UUID
      const pdfPages = await this.extractPdfPages(fileBuffer, documentId);

      if (pdfPages && pdfPages.length > 0) {
        fullText = pdfPages.map(page => page.text).join('\n\n');
        this.logger.debug(`Extracted text from ${pdfPages.length} pages using extractPdfPages.`);
      } else {
        this.logger.debug('extractPdfPages returned no pages or an empty array.');
      }

      // If extraction yields little or no text, fall back to the command-line method
      if (fullText.trim().length < 100) {
        this.logger.warn('Text extracted via extractPdfPages is less than 100 characters. Falling back to command-line method.');
        fullText = await this.extractTextUsingCommandLine(fileBuffer);
      }

      this.logger.debug(`Successfully extracted ${fullText.length} characters of text from PDF`);
      return fullText;
    } catch (error) {
      this.logger.error(`Error extracting full text using extractPdfPages: ${error.message}`, error.stack);
      this.logger.debug('Falling back to command-line method for text extraction due to error.');
      try {
        fullText = await this.extractTextUsingCommandLine(fileBuffer);
        this.logger.debug(`Successfully extracted ${fullText.length} characters using fallback command-line method.`);
        return fullText;
      } catch (fallbackError) {
        this.logger.error(`Fallback extraction also failed: ${fallbackError.message}`, fallbackError.stack);
        throw new Error(`Failed to extract text from PDF after multiple attempts: ${fallbackError.message}`);
      }
    }
  }

  /**
   * Extract text from PDF using command-line tools
   * @param fileBuffer The PDF file buffer
   * @returns A string containing all text from the PDF
   * @private
   */
  private async extractTextUsingCommandLine(fileBuffer: Buffer): Promise<string> {
    try {
      // Create a temporary file to store the PDF
      const tempId = crypto.randomBytes(16).toString('hex');
      const tempPdfPath = path.join(this.imageStoragePath, `${tempId}-temp.pdf`);
      await promisify(fs.writeFile)(tempPdfPath, fileBuffer);

      // Use pdftotext to extract all text at once
      const execPromise = promisify(exec);
      const { stdout } = await execPromise(`pdftotext -layout ${tempPdfPath} -`);

      // Clean up the temporary file
      try {
        await promisify(fs.unlink)(tempPdfPath);
      } catch (unlinkError) {
        this.logger.error(`Error removing temporary PDF file: ${unlinkError.message}`);
      }

      return stdout.trim();
    } catch (error) {
      this.logger.error(`Error extracting text using command line: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async processWithQwen(text: string, imageUrl: string | null): Promise<string> {
    try {
      // If there's no image, just return the text
      if (!imageUrl) {
        return text;
      }

      // Convert relative URL to absolute file path
      const imageFilename = path.basename(imageUrl);
      const imagePath = path.join(this.imageStoragePath, imageFilename);

      // Read the image file and convert to base64
      const imageBuffer = await promisify(fs.readFile)(imagePath);
      const base64Image = imageBuffer.toString('base64');

      // Prepare the prompt for the multimodal model
      const prompt = `
      You are an educational content analyzer. Examine the following image and text from an educational document.

      The image may contain diagrams, charts, equations, or other visual elements related to the text.

      Please provide a comprehensive description of both the text and visual content. If the image contains:
      1. Diagrams or figures: Describe what they show and how they relate to the text
      2. Mathematical equations or formulas: Transcribe them accurately
      3. Charts or graphs: Describe the data and trends shown
      4. Tables: Summarize the information in the table
      5. Questions or problems: Identify them and describe any visual elements related to them

      Text content: ${text}
      `;

      // Call the Qwen 2.5 VL model via OpenRouter API
      const response = await axios.post(
        this.apiEndpoint,
        {
          model: this.modelConfigService.getMultimodalModel(),
          messages: [
            {
              role: 'user',
              content: [
                { type: 'text', text: prompt },
                { type: 'image_url', image_url: { url: `data:image/png;base64,${base64Image}` } }
              ]
            }
          ],
          max_tokens: 1024
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          }
        }
      );

      // Extract the enhanced content from the response
      const enhancedContent = response.data.choices[0].message.content;

      // Combine the original text with the enhanced content
      return `${text}\n\nImage Analysis:\n${enhancedContent}`;
    } catch (error) {
      this.logger.error(`Error processing with Qwen model: ${error.message}`, error.stack);
      // Return the original text if processing fails
      return text;
    }
  }
}