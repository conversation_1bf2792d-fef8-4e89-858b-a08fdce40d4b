import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Logger } from '@nestjs/common';
import { QuestionPoolService } from './question-pool.service';
import { QuestionPool } from '../mongodb/schemas/question-pool.schema';
import { QuestionPoolCacheService } from './services/question-pool-cache.service';
import { QuestionPoolMetricsService } from './services/question-pool-metrics.service';
import { PoolMonitoringService } from '../monitoring/services/pool-monitoring.service';
import { 
  QuestionSelectionParams, 
  DifficultyDistribution,
  QuestionTypeBalancing,
  DiversityConfig,
  QualityValidationConfig,
  FallbackConfig 
} from './interfaces/distribution.interface';

describe('QuestionPoolService - Distribution Algorithms', () => {
  let service: QuestionPoolService;
  let mockQuestionPoolModel: any;
  let mockCacheService: jest.Mocked<QuestionPoolCacheService>;
  let mockMetricsService: jest.Mocked<QuestionPoolMetricsService>;
  let mockMonitoringService: jest.Mocked<PoolMonitoringService>;

  // Mock question data with different difficulty levels and types
  const mockEasyQuestions = Array.from({ length: 10 }, (_, i) => ({
    _id: `easy_${i}`,
    content: `Easy question ${i}`,
    type: i % 2 === 0 ? 'multiple_choice' : 'fill_blank',
    difficulty: 'Easy',
    subject: 'Mathematics',
    grade: 'Primary 2',
    status: 'active',
    lastSelected: i < 3 ? new Date(Date.now() - 1000 * 60 * 60) : null, // Some recently selected
    selectionCount: i < 3 ? 5 : 1, // Some frequently selected
  }));

  const mockMediumQuestions = Array.from({ length: 15 }, (_, i) => ({
    _id: `medium_${i}`,
    content: `Medium question ${i}`,
    type: i % 3 === 0 ? 'multiple_choice' : i % 3 === 1 ? 'fill_blank' : 'creative_writing',
    difficulty: 'Medium',
    subject: 'Mathematics',
    grade: 'Primary 4',
    status: 'active',
    lastSelected: i < 2 ? new Date(Date.now() - 1000 * 60 * 30) : null,
    selectionCount: i < 2 ? 3 : 0,
  }));

  const mockAdvancedQuestions = Array.from({ length: 5 }, (_, i) => ({
    _id: `advanced_${i}`,
    content: `Advanced question ${i}`,
    type: i % 2 === 0 ? 'multiple_choice' : 'essay',
    difficulty: 'Advanced',
    subject: 'Mathematics',
    grade: 'Secondary 1',
    status: 'active',
    lastSelected: null,
    selectionCount: 0,
  }));

  beforeEach(async () => {
    // Create mock aggregation pipeline
    const mockAggregate = jest.fn();
    
    // Create mock model
    mockQuestionPoolModel = {
      aggregate: mockAggregate,
      distinct: jest.fn(),
      countDocuments: jest.fn(),
    };

    // Create mock services
    mockCacheService = {
      getFromCache: jest.fn().mockResolvedValue(null),
      saveToCache: jest.fn(),
      invalidateCache: jest.fn(),
      generateCacheKey: jest.fn(),
    } as any;

    mockMetricsService = {
      recordQuery: jest.fn(),
      recordQueryDuration: jest.fn(),
      recordCacheHit: jest.fn(),
      recordCacheMiss: jest.fn(),
      updateQuestionCounts: jest.fn(),
    } as any;

    mockMonitoringService = {
      emitEvent: jest.fn(),
      getEvents: jest.fn(),
      calculatePoolUtilization: jest.fn(),
      calculateQuestionReuse: jest.fn(),
      calculateGenerationTimeComparison: jest.fn(),
      calculateValidationMetrics: jest.fn(),
      calculateCacheMetrics: jest.fn(),
      getDashboardMetrics: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuestionPoolService,
        {
          provide: getModelToken(QuestionPool.name),
          useValue: mockQuestionPoolModel,
        },
        {
          provide: QuestionPoolCacheService,
          useValue: mockCacheService,
        },
        {
          provide: QuestionPoolMetricsService,
          useValue: mockMetricsService,
        },
        {
          provide: PoolMonitoringService,
          useValue: mockMonitoringService,
        },
        {
          provide: Logger,
          useValue: {
            debug: jest.fn(),
            log: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<QuestionPoolService>(QuestionPoolService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Difficulty Distribution Algorithm', () => {
    it('should calculate correct target counts for difficulty distribution', async () => {
      const difficultyDistribution: DifficultyDistribution = {
        Easy: 0.3,    // 30%
        Medium: 0.5,  // 50%
        Advanced: 0.2, // 20%
      };

      const totalCount = 10;
      
      // Mock the aggregation to return questions for each difficulty level
      let callCount = 0;
      mockQuestionPoolModel.aggregate.mockImplementation(() => ({
        exec: jest.fn().mockImplementation(() => {
          callCount++;
          if (callCount === 1) {
            // Easy questions (should get 3 questions)
            return Promise.resolve(mockEasyQuestions.slice(0, 3));
          } else if (callCount === 2) {
            // Medium questions (should get 5 questions)
            return Promise.resolve(mockMediumQuestions.slice(0, 5));
          } else {
            // Advanced questions (should get 2 questions)
            return Promise.resolve(mockAdvancedQuestions.slice(0, 2));
          }
        }),
      }));

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: totalCount,
        distributionConfig: {
          difficultyDistribution,
        },
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(10);
      expect(mockQuestionPoolModel.aggregate).toHaveBeenCalledTimes(3); // One call per difficulty level
      
      // Verify the distribution was achieved
      const easyCount = result.questions.filter(q => q.difficulty === 'Easy').length;
      const mediumCount = result.questions.filter(q => q.difficulty === 'Medium').length;
      const advancedCount = result.questions.filter(q => q.difficulty === 'Advanced').length;
      
      expect(easyCount).toBe(3);
      expect(mediumCount).toBe(5);
      expect(advancedCount).toBe(2);
    });

    it('should handle uneven distribution with proper rounding', async () => {
      const difficultyDistribution: DifficultyDistribution = {
        Easy: 0.33,   // 33% of 7 = 2.31 → 2
        Medium: 0.34, // 34% of 7 = 2.38 → 2  
        Advanced: 0.33, // 33% of 7 = 2.31 → 3 (remainder)
      };

      let callCount = 0;
      mockQuestionPoolModel.aggregate.mockImplementation(() => ({
        exec: jest.fn().mockImplementation(() => {
          callCount++;
          if (callCount === 1) {
            return Promise.resolve(mockEasyQuestions.slice(0, 2));
          } else if (callCount === 2) {
            return Promise.resolve(mockMediumQuestions.slice(0, 2));
          } else {
            return Promise.resolve(mockAdvancedQuestions.slice(0, 3));
          }
        }),
      }));

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 7,
        distributionConfig: {
          difficultyDistribution,
        },
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(7);
      
      const easyCount = result.questions.filter(q => q.difficulty === 'Easy').length;
      const mediumCount = result.questions.filter(q => q.difficulty === 'Medium').length;
      const advancedCount = result.questions.filter(q => q.difficulty === 'Advanced').length;
      
      expect(easyCount + mediumCount + advancedCount).toBe(7);
      expect(easyCount).toBeGreaterThanOrEqual(2);
      expect(mediumCount).toBeGreaterThanOrEqual(2);
      expect(advancedCount).toBeGreaterThanOrEqual(2);
    });

    it('should handle insufficient questions for a difficulty level', async () => {
      const difficultyDistribution: DifficultyDistribution = {
        Easy: 0.5,    // 50% - requesting 5 questions
        Medium: 0.3,  // 30% - requesting 3 questions
        Advanced: 0.2, // 20% - requesting 2 questions
      };

      let callCount = 0;
      mockQuestionPoolModel.aggregate.mockImplementation(() => ({
        exec: jest.fn().mockImplementation(() => {
          callCount++;
          if (callCount === 1) {
            // Only 2 Easy questions available instead of 5
            return Promise.resolve(mockEasyQuestions.slice(0, 2));
          } else if (callCount === 2) {
            // 3 Medium questions as expected
            return Promise.resolve(mockMediumQuestions.slice(0, 3));
          } else {
            // 2 Advanced questions as expected
            return Promise.resolve(mockAdvancedQuestions.slice(0, 2));
          }
        }),
      }));

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 10,
        distributionConfig: {
          difficultyDistribution,
        },
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(7); // 2 + 3 + 2 = 7 instead of 10
      expect(result.metadata.totalRequested).toBe(10);
      expect(result.metadata.totalReturned).toBe(7);
      expect(result.metadata.fallbacksTriggered).toContain('insufficient_easy_questions');
    });
  });

  describe('Question Type Balancing Algorithm', () => {
    it('should balance question types when preferDiversity is enabled', async () => {
      const questionTypeBalancing: QuestionTypeBalancing = {
        enabled: true,
        preferDiversity: true,
      };

      // Mock distinct call to return available question types
      mockQuestionPoolModel.distinct.mockResolvedValue(['multiple_choice', 'fill_blank', 'creative_writing']);

      let callCount = 0;
      mockQuestionPoolModel.aggregate.mockImplementation(() => ({
        exec: jest.fn().mockImplementation(() => {
          callCount++;
          if (callCount === 1) {
            // multiple_choice questions
            return Promise.resolve([
              { ...mockMediumQuestions[0], type: 'multiple_choice' },
              { ...mockMediumQuestions[3], type: 'multiple_choice' },
            ]);
          } else if (callCount === 2) {
            // fill_blank questions
            return Promise.resolve([
              { ...mockMediumQuestions[1], type: 'fill_blank' },
              { ...mockMediumQuestions[4], type: 'fill_blank' },
            ]);
          } else {
            // creative_writing questions
            return Promise.resolve([
              { ...mockMediumQuestions[2], type: 'creative_writing' },
              { ...mockMediumQuestions[5], type: 'creative_writing' },
            ]);
          }
        }),
      }));

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 6,
        distributionConfig: {
          questionTypeBalancing,
        },
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(6);
      expect(mockQuestionPoolModel.distinct).toHaveBeenCalledWith('type', expect.any(Object));
      
      // Verify type distribution
      const typeDistribution = result.questions.reduce((acc, q) => {
        acc[q.type] = (acc[q.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      expect(Object.keys(typeDistribution)).toHaveLength(3);
      expect(typeDistribution['multiple_choice']).toBe(2);
      expect(typeDistribution['fill_blank']).toBe(2);
      expect(typeDistribution['creative_writing']).toBe(2);
    });

    it('should use specific target distribution when provided', async () => {
      const questionTypeBalancing: QuestionTypeBalancing = {
        enabled: true,
        preferDiversity: false,
        targetDistribution: {
          'multiple_choice': 0.6, // 60%
          'fill_blank': 0.4,      // 40%
        },
      };

      let callCount = 0;
      mockQuestionPoolModel.aggregate.mockImplementation(() => ({
        exec: jest.fn().mockImplementation(() => {
          callCount++;
          if (callCount === 1) {
            // multiple_choice questions (should get 6 out of 10)
            return Promise.resolve(mockMediumQuestions.slice(0, 6).map(q => ({ ...q, type: 'multiple_choice' })));
          } else {
            // fill_blank questions (should get 4 out of 10)
            return Promise.resolve(mockMediumQuestions.slice(6, 10).map(q => ({ ...q, type: 'fill_blank' })));
          }
        }),
      }));

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 10,
        distributionConfig: {
          questionTypeBalancing,
        },
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(10);
      
      const typeDistribution = result.questions.reduce((acc, q) => {
        acc[q.type] = (acc[q.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      expect(typeDistribution['multiple_choice']).toBe(6);
      expect(typeDistribution['fill_blank']).toBe(4);
    });
  });

  describe('Diversity Algorithm', () => {
    it('should penalize recently selected questions', async () => {
      const diversityConfig: DiversityConfig = {
        enabled: true,
        recencyPenaltyWeight: 0.8,
        frequencyPenaltyWeight: 0.2,
        recencyThresholdHours: 2, // 2 hours
      };

      // Mock questions where some were recently selected
      const questionsWithRecency = [
        { ...mockEasyQuestions[0], lastSelected: new Date(Date.now() - 1000 * 60 * 30) }, // 30 min ago
        { ...mockEasyQuestions[1], lastSelected: new Date(Date.now() - 1000 * 60 * 60 * 3) }, // 3 hours ago
        { ...mockEasyQuestions[2], lastSelected: null }, // Never selected
      ];

      mockQuestionPoolModel.aggregate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(questionsWithRecency),
      });

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 2,
        distributionConfig: {
          diversity: diversityConfig,
        },
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(2);
      expect(mockMonitoringService.emitEvent).toHaveBeenCalled();

      // The algorithm should prefer questions that weren't recently selected
      const selectedIds = result.questions.map(q => q._id);
      expect(selectedIds).toContain(mockEasyQuestions[2]._id); // Never selected should be preferred
    });

    it('should penalize frequently selected questions', async () => {
      const diversityConfig: DiversityConfig = {
        enabled: true,
        recencyPenaltyWeight: 0.2,
        frequencyPenaltyWeight: 0.8,
        recencyThresholdHours: 24,
      };

      // Mock questions with different selection frequencies
      const questionsWithFrequency = [
        { ...mockEasyQuestions[0], selectionCount: 10 }, // Frequently selected
        { ...mockEasyQuestions[1], selectionCount: 2 },  // Moderately selected
        { ...mockEasyQuestions[2], selectionCount: 0 },  // Never selected
      ];

      mockQuestionPoolModel.aggregate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(questionsWithFrequency),
      });

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 2,
        distributionConfig: {
          diversity: diversityConfig,
        },
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(2);

      // The algorithm should prefer questions with lower selection counts
      const selectedIds = result.questions.map(q => q._id);
      expect(selectedIds).toContain(mockEasyQuestions[2]._id); // Never selected should be preferred
    });

    it('should skip diversity when disabled', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 3,
        skipDiversity: true,
      };

      mockQuestionPoolModel.aggregate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockEasyQuestions.slice(0, 3)),
      });

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(3);
      // Should use simple random selection without diversity weighting
      expect(mockQuestionPoolModel.aggregate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ $match: expect.any(Object) }),
          expect.objectContaining({ $sample: expect.any(Object) }),
        ])
      );
    });
  });

  describe('Quality Validation Integration', () => {
    it('should validate selected questions when validation is enabled', async () => {
      const qualityValidation: QualityValidationConfig = {
        enabled: true,
        failureHandlingStrategy: 'replace',
        maxReplacementAttempts: 3,
        minValidationSuccessRate: 0.8,
      };

      mockQuestionPoolModel.aggregate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockEasyQuestions.slice(0, 5)),
      });

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 3,
        distributionConfig: {
          qualityValidation,
        },
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(3);
      expect(result.metadata.validationStats).toBeDefined();
      expect(result.metadata.validationStats.totalValidated).toBeGreaterThan(0);
    });

    it('should handle validation failures with discard strategy', async () => {
      const qualityValidation: QualityValidationConfig = {
        enabled: true,
        failureHandlingStrategy: 'discard',
        maxReplacementAttempts: 0,
        minValidationSuccessRate: 0.5,
      };

      mockQuestionPoolModel.aggregate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockEasyQuestions.slice(0, 5)),
      });

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 3,
        distributionConfig: {
          qualityValidation,
        },
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.metadata.validationStats).toBeDefined();
      expect(mockMonitoringService.emitEvent).toHaveBeenCalled();
    });

    it('should skip validation when disabled', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 3,
        skipValidation: true,
      };

      mockQuestionPoolModel.aggregate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockEasyQuestions.slice(0, 3)),
      });

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(3);
      expect(result.metadata.validationStats.totalValidated).toBe(0);
    });
  });

  describe('Fallback Strategies', () => {
    it('should apply best effort fallback when exact criteria cannot be met', async () => {
      const fallbackConfig: FallbackConfig = {
        allowBestEffort: true,
        relaxDistributionOnShortfall: true,
        logFallbackReasons: true,
      };

      // Mock insufficient questions scenario
      mockQuestionPoolModel.aggregate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockEasyQuestions.slice(0, 2)), // Only 2 questions available
      });

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 10, // Requesting 10 questions
        distributionConfig: {
          fallback: fallbackConfig,
        },
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(2);
      expect(result.metadata.totalRequested).toBe(10);
      expect(result.metadata.totalReturned).toBe(2);
      expect(result.metadata.fallbacksTriggered).toContain('best_effort_shortfall');
    });

    it('should relax distribution rules when shortfall occurs', async () => {
      const fallbackConfig: FallbackConfig = {
        allowBestEffort: true,
        relaxDistributionOnShortfall: true,
        logFallbackReasons: true,
      };

      const difficultyDistribution: DifficultyDistribution = {
        Easy: 0.5,    // 50% - requesting 5 questions
        Medium: 0.3,  // 30% - requesting 3 questions
        Advanced: 0.2, // 20% - requesting 2 questions
      };

      let callCount = 0;
      mockQuestionPoolModel.aggregate.mockImplementation(() => ({
        exec: jest.fn().mockImplementation(() => {
          callCount++;
          if (callCount === 1) {
            // No Easy questions available
            return Promise.resolve([]);
          } else if (callCount === 2) {
            // All Medium questions
            return Promise.resolve(mockMediumQuestions.slice(0, 8));
          } else {
            // Some Advanced questions
            return Promise.resolve(mockAdvancedQuestions.slice(0, 2));
          }
        }),
      }));

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 10,
        distributionConfig: {
          difficultyDistribution,
          fallback: fallbackConfig,
        },
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(10);
      expect(result.metadata.fallbacksTriggered).toContain('relaxed_distribution');

      // Should have more Medium questions than originally planned due to fallback
      const mediumCount = result.questions.filter(q => q.difficulty === 'Medium').length;
      expect(mediumCount).toBeGreaterThan(3);
    });
  });

  describe('Combined Algorithm Integration', () => {
    it('should apply all algorithms together when enabled', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 6,
        distributionConfig: {
          difficultyDistribution: {
            Easy: 0.33,
            Medium: 0.34,
            Advanced: 0.33,
          },
          questionTypeBalancing: {
            enabled: true,
            preferDiversity: true,
          },
          diversity: {
            enabled: true,
            recencyPenaltyWeight: 0.5,
            frequencyPenaltyWeight: 0.5,
            recencyThresholdHours: 24,
          },
          qualityValidation: {
            enabled: true,
            failureHandlingStrategy: 'replace',
            maxReplacementAttempts: 2,
            minValidationSuccessRate: 0.8,
          },
          fallback: {
            allowBestEffort: true,
            relaxDistributionOnShortfall: false,
            logFallbackReasons: true,
          },
        },
      };

      // Mock complex aggregation scenario
      let callCount = 0;
      mockQuestionPoolModel.aggregate.mockImplementation(() => ({
        exec: jest.fn().mockImplementation(() => {
          callCount++;
          if (callCount <= 3) {
            // Difficulty-based calls
            return Promise.resolve(mockEasyQuestions.slice(0, 2));
          } else {
            // Type balancing calls
            return Promise.resolve(mockMediumQuestions.slice(0, 2));
          }
        }),
      }));

      mockQuestionPoolModel.distinct.mockResolvedValue(['multiple_choice', 'fill_blank']);

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toBeDefined();
      expect(result.metadata.distributionAchieved).toBeDefined();
      expect(result.metadata.validationStats).toBeDefined();
      expect(mockMonitoringService.emitEvent).toHaveBeenCalled();
    });
  });
});
