import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Logger } from '@nestjs/common';
import { QuestionPoolService } from '../question-pool.service';
import { QuestionPool } from '../../mongodb/schemas/question-pool.schema';
import { QuestionPoolCacheService } from '../services/question-pool-cache.service';
import { QuestionPoolMetricsService } from '../services/question-pool-metrics.service';
import { PoolMonitoringService } from '../../monitoring/services/pool-monitoring.service';
import { ContentValidationService } from '../../validation/content-validation.service';
import { QuestionPoolConfigService } from '../question-pool-config.service';
import { WorksheetDocumentCacheService } from '../../worksheet/services/worksheet-document-cache.service';
import { 
  QuestionSelectionParams, 
  QuestionSelectionResult
} from '../interfaces/distribution.interface';

describe('QuestionPoolService - Service Integration Tests', () => {
  let questionPoolService: QuestionPoolService;
  let cacheService: QuestionPoolCacheService;
  let configService: QuestionPoolConfigService;
  let metricsService: QuestionPoolMetricsService;
  let mockQuestionPoolModel: any;

  // Mock question data
  const mockQuestions = [
    {
      _id: '507f1f77bcf86cd799439011',
      content: 'What is 2 + 2?',
      type: 'multiple_choice',
      options: ['3', '4', '5', '6'],
      answer: '4',
      subject: 'Mathematics',
      parentSubject: 'Mathematics',
      childSubject: 'Addition',
      grade: 'Primary 2',
      language: 'English',
      difficulty: 'Easy',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      _id: '507f1f77bcf86cd799439012',
      content: 'What is 5 × 3?',
      type: 'multiple_choice',
      options: ['12', '15', '18', '20'],
      answer: '15',
      subject: 'Mathematics',
      parentSubject: 'Mathematics',
      childSubject: 'Multiplication',
      grade: 'Primary 3',
      language: 'English',
      difficulty: 'Medium',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  beforeEach(async () => {
    // Create mock aggregation pipeline
    const mockAggregate = jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue(mockQuestions),
    });

    // Create mock model
    mockQuestionPoolModel = {
      aggregate: mockAggregate,
      find: jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockQuestions),
      }),
      countDocuments: jest.fn().mockResolvedValue(100),
      distinct: jest.fn().mockResolvedValue(['Easy', 'Medium', 'Advanced']),
      updateMany: jest.fn().mockResolvedValue({ modifiedCount: 2 }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuestionPoolService,
        {
          provide: getModelToken(QuestionPool.name),
          useValue: mockQuestionPoolModel,
        },
        {
          provide: QuestionPoolCacheService,
          useValue: {
            getFromCache: jest.fn(),
            saveToCache: jest.fn(),
            invalidateCache: jest.fn(),
            generateCacheKey: jest.fn(),
          },
        },
        {
          provide: QuestionPoolMetricsService,
          useValue: {
            recordQuery: jest.fn(),
            recordQueryDuration: jest.fn(),
            recordCacheHit: jest.fn(),
            recordCacheMiss: jest.fn(),
            updateQuestionCounts: jest.fn(),
            recordError: jest.fn(),
          },
        },
        {
          provide: PoolMonitoringService,
          useValue: {
            emitEvent: jest.fn(),
            getEvents: jest.fn(),
            calculatePoolUtilization: jest.fn(),
            calculateQuestionReuse: jest.fn(),
            calculateGenerationTimeComparison: jest.fn(),
            calculateValidationMetrics: jest.fn(),
            calculateCacheMetrics: jest.fn(),
            getDashboardMetrics: jest.fn(),
          },
        },
        {
          provide: ContentValidationService,
          useValue: {
            validateQuestion: jest.fn().mockResolvedValue({ isValid: true, score: 0.9 }),
            validateQuestions: jest.fn().mockResolvedValue({
              results: [
                { isValid: true, score: 0.9 },
                { isValid: true, score: 0.8 },
              ],
              summary: {
                overallScore: 0.85,
                passedCount: 2,
                failedCount: 0,
                successRate: 1.0,
              },
            }),
            getValidationRules: jest.fn().mockReturnValue([]),
          },
        },
        {
          provide: QuestionPoolConfigService,
          useValue: {
            getConfig: jest.fn().mockReturnValue({
              enabled: true,
              defaultSelectionStrategy: 'hybrid',
              minPoolQuestionsThreshold: 10,
              distribution: {
                defaultDifficultyDistribution: { Easy: 0.3, Medium: 0.4, Advanced: 0.3 },
                defaultQuestionTypeBalancing: { enabled: true, preferDiversity: true },
                defaultDiversityConfig: { enabled: true, recencyPenaltyWeight: 0.3, frequencyPenaltyWeight: 0.2, recencyThresholdHours: 24 },
                defaultQualityValidationConfig: { enabled: true, failureHandlingStrategy: 'replace', maxReplacementAttempts: 3, minValidationSuccessRate: 0.8 },
                defaultFallbackConfig: { allowBestEffort: true, relaxDistributionOnShortfall: true, logFallbackReasons: true },
              },
              featureFlags: {
                allowPoolOnlyStrategy: true,
                allowAiOnlyStrategy: true,
                allowHybridStrategy: true,
                allowMixedStrategy: true,
              },
            }),
            isQuestionPoolEnabled: jest.fn().mockReturnValue(true),
            getDefaultSelectionStrategy: jest.fn().mockReturnValue('hybrid'),
          },
        },
        {
          provide: WorksheetDocumentCacheService,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            delete: jest.fn(),
            clear: jest.fn(),
            generateKey: jest.fn(),
          },
        },
        {
          provide: Logger,
          useValue: {
            debug: jest.fn(),
            log: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
          },
        },
      ],
    }).compile();

    questionPoolService = module.get<QuestionPoolService>(QuestionPoolService);
    cacheService = module.get<QuestionPoolCacheService>(QuestionPoolCacheService);
    configService = module.get<QuestionPoolConfigService>(QuestionPoolConfigService);
    metricsService = module.get<QuestionPoolMetricsService>(QuestionPoolMetricsService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Integration with Caching', () => {
    it('should use cached results when available', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        grade: 'Primary 2',
        count: 2,
      };

      const cachedResult: QuestionSelectionResult = {
        questions: mockQuestions,
        metadata: {
          totalRequested: 2,
          totalReturned: 2,
          distributionAchieved: { Easy: 0.5, Medium: 0.5 },
          fallbacksTriggered: [],
          validationStats: {
            totalValidated: 2,
            passed: 2,
            failed: 0,
            successRate: 1.0,
          },
          selectionTime: 50,
          cachedAt: new Date(),
        },
      };

      // Mock cache hit
      const mockGetFromCache = jest.spyOn(cacheService, 'getFromCache');
      mockGetFromCache.mockResolvedValue(cachedResult);

      const result = await questionPoolService.getRandomQuestionsWithDistribution(params);

      expect(result).toEqual(cachedResult);
      expect(mockGetFromCache).toHaveBeenCalled();
      expect(mockQuestionPoolModel.aggregate).not.toHaveBeenCalled(); // Should not hit database
      expect(metricsService.recordCacheHit).toHaveBeenCalled();
    });

    it('should cache results on cache miss', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        grade: 'Primary 2',
        count: 2,
      };

      // Mock cache miss
      const mockGetFromCache = jest.spyOn(cacheService, 'getFromCache');
      mockGetFromCache.mockResolvedValue(null);

      const mockSaveToCache = jest.spyOn(cacheService, 'saveToCache');
      mockSaveToCache.mockResolvedValue(undefined);

      const result = await questionPoolService.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(2);
      expect(mockGetFromCache).toHaveBeenCalled();
      expect(mockSaveToCache).toHaveBeenCalled();
      expect(mockQuestionPoolModel.aggregate).toHaveBeenCalled(); // Should hit database
      expect(metricsService.recordCacheMiss).toHaveBeenCalled();
    });

    it('should handle cache errors gracefully', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        grade: 'Primary 2',
        count: 2,
      };

      // Mock cache error
      const mockGetFromCache = jest.spyOn(cacheService, 'getFromCache');
      mockGetFromCache.mockRejectedValue(new Error('Cache service unavailable'));

      const result = await questionPoolService.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(2);
      expect(mockQuestionPoolModel.aggregate).toHaveBeenCalled(); // Should fallback to database
    });
  });

  describe('Service Integration with Configuration', () => {
    it('should apply configuration from ConfigService', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 5,
      };

      const result = await questionPoolService.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toBeDefined();
      expect(configService.getConfig).toHaveBeenCalled();
    });

    it('should respect configuration overrides in request', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 3,
        distributionConfig: {
          difficultyDistribution: {
            Easy: 0.5,
            Medium: 0.3,
            Advanced: 0.2,
          },
          qualityValidation: {
            enabled: false,
            failureHandlingStrategy: 'discard',
            maxReplacementAttempts: 0,
            minValidationSuccessRate: 0,
          },
        },
      };

      const result = await questionPoolService.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toBeDefined();
      expect(result.metadata.validationStats.totalValidated).toBe(0); // Validation disabled
    });
  });

  describe('Service Integration with Metrics', () => {
    it('should record metrics for successful operations', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 2,
      };

      await questionPoolService.getRandomQuestionsWithDistribution(params);

      expect(metricsService.recordQuery).toHaveBeenCalledWith('getRandomQuestionsWithDistribution', 'success');
      expect(metricsService.recordQueryDuration).toHaveBeenCalled();
      expect(metricsService.updateQuestionCounts).toHaveBeenCalled();
    });

    it('should record metrics for error scenarios', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 2,
      };

      // Mock database error
      mockQuestionPoolModel.aggregate.mockReturnValue({
        exec: jest.fn().mockRejectedValue(new Error('Database connection failed')),
      });

      await expect(questionPoolService.getRandomQuestionsWithDistribution(params)).rejects.toThrow();

      expect(metricsService.recordError).toHaveBeenCalled();
      expect(metricsService.recordQuery).toHaveBeenCalledWith('getRandomQuestionsWithDistribution', 'error');
    });
  });

  describe('End-to-End Question Selection Flow', () => {
    it('should complete full question selection with all integrations', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        grade: 'Primary 2',
        count: 5,
        distributionConfig: {
          difficultyDistribution: {
            Easy: 0.4,
            Medium: 0.4,
            Advanced: 0.2,
          },
          questionTypeBalancing: {
            enabled: true,
            preferDiversity: true,
          },
          diversity: {
            enabled: true,
            recencyPenaltyWeight: 0.5,
            frequencyPenaltyWeight: 0.3,
            recencyThresholdHours: 24,
          },
          qualityValidation: {
            enabled: true,
            failureHandlingStrategy: 'replace',
            maxReplacementAttempts: 2,
            minValidationSuccessRate: 0.8,
          },
        },
      };

      // Mock cache miss to ensure full flow
      const mockGetFromCache = jest.spyOn(cacheService, 'getFromCache');
      mockGetFromCache.mockResolvedValue(null);

      const result = await questionPoolService.getRandomQuestionsWithDistribution(params);

      // Verify the complete flow
      expect(result).toBeDefined();
      expect(result.questions).toHaveLength(2); // Based on mock data
      expect(result.metadata).toBeDefined();
      expect(result.metadata.totalRequested).toBe(5);
      expect(result.metadata.totalReturned).toBe(2);
      expect(result.metadata.distributionAchieved).toBeDefined();
      expect(result.metadata.validationStats).toBeDefined();
      expect(result.metadata.selectionTime).toBeGreaterThan(0);

      // Verify all services were called
      expect(configService.getConfig).toHaveBeenCalled();
      expect(cacheService.getFromCache).toHaveBeenCalled();
      expect(cacheService.saveToCache).toHaveBeenCalled();
      expect(metricsService.recordQuery).toHaveBeenCalled();
      expect(mockQuestionPoolModel.aggregate).toHaveBeenCalled();
    });
  });
});
