import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { QuestionPoolService } from './question-pool.service';
import { QuestionPoolConfigService } from './question-pool-config.service';
import { ContentValidationService } from '../validation/content-validation.service';
import { QuestionPool } from '../mongodb/schemas/question-pool.schema';
import { QuestionSelectionParams } from './interfaces/distribution.interface';

describe('QuestionPoolService - Distribution Features', () => {
  let service: QuestionPoolService;
  let mockQuestionPoolModel: any;
  let mockConfigService: any;
  let mockValidationService: any;

  beforeEach(async () => {
    // Mock the Mongoose model
    mockQuestionPoolModel = {
      aggregate: jest.fn().mockReturnValue({
        exec: jest.fn(),
      }),
      distinct: jest.fn(),
      updateMany: jest.fn().mockResolvedValue({ modifiedCount: 0 }),
      constructor: jest.fn(),
    };

    // Mock the config service
    mockConfigService = {
      getConfig: jest.fn().mockReturnValue({
        distribution: {
          defaultDifficultyDistribution: {
            Easy: 0.2,
            Medium: 0.6,
            Advanced: 0.2,
          },
          defaultQuestionTypeBalancing: {
            enabled: true,
            preferDiversity: true,
          },
          defaultDiversityConfig: {
            enabled: true,
            recencyPenaltyWeight: 0.3,
            frequencyPenaltyWeight: 0.2,
            recencyThresholdHours: 24,
          },
          defaultQualityValidationConfig: {
            enabled: true,
            failureHandlingStrategy: 'replace',
            maxReplacementAttempts: 3,
            minValidationSuccessRate: 0.8,
          },
          defaultFallbackConfig: {
            allowBestEffort: true,
            relaxDistributionOnShortfall: true,
            logFallbackReasons: true,
          },
        },
      }),
    };

    // Mock the validation service
    mockValidationService = {
      validateQuestions: jest.fn().mockResolvedValue({
        results: [],
        summary: {
          totalValidated: 0,
          passed: 0,
          failed: 0,
          successRate: 1,
          averageScore: 1,
        },
        processingTime: 0,
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuestionPoolService,
        {
          provide: getModelToken(QuestionPool.name),
          useValue: mockQuestionPoolModel,
        },
        {
          provide: QuestionPoolConfigService,
          useValue: mockConfigService,
        },
        {
          provide: ContentValidationService,
          useValue: mockValidationService,
        },
      ],
    }).compile();

    service = module.get<QuestionPoolService>(QuestionPoolService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getRandomQuestionsWithDistribution', () => {
    it('should perform simple random selection when distribution is skipped', async () => {
      const mockQuestions = [
        { _id: '1', type: 'multiple_choice', content: 'Test question 1', difficulty: 'Easy' },
        { _id: '2', type: 'fill_blank', content: 'Test question 2', difficulty: 'Medium' },
      ];

      mockQuestionPoolModel.aggregate().exec.mockResolvedValue(mockQuestions);
      mockQuestionPoolModel.constructor.mockImplementation((doc) => doc);

      const params: QuestionSelectionParams = {
        count: 2,
        skipDistribution: true,
        skipDiversity: true,
        skipValidation: true,
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(2);
      expect(result.metadata.totalRequested).toBe(2);
      expect(result.metadata.totalReturned).toBe(2);
      expect(result.metadata.fallbacksTriggered).toHaveLength(0);
    });

    it('should enforce difficulty distribution when enabled', async () => {
      // Mock distinct method for type diversity
      mockQuestionPoolModel.distinct.mockResolvedValue(['multiple_choice', 'fill_blank', 'open_ended']);

      // Mock different responses for different difficulty levels and type diversity calls
      mockQuestionPoolModel.aggregate().exec
        // Easy difficulty - type diversity calls
        .mockResolvedValueOnce([
          { _id: '1', type: 'multiple_choice', content: 'Easy MC question', difficulty: 'Easy' },
        ])
        .mockResolvedValueOnce([
          { _id: '2', type: 'fill_blank', content: 'Easy FB question', difficulty: 'Easy' },
        ])
        .mockResolvedValueOnce([])  // No open_ended Easy questions
        // Medium difficulty - type diversity calls
        .mockResolvedValueOnce([
          { _id: '3', type: 'multiple_choice', content: 'Medium MC question 1', difficulty: 'Medium' },
          { _id: '4', type: 'multiple_choice', content: 'Medium MC question 2', difficulty: 'Medium' },
        ])
        .mockResolvedValueOnce([
          { _id: '5', type: 'fill_blank', content: 'Medium FB question 1', difficulty: 'Medium' },
          { _id: '6', type: 'fill_blank', content: 'Medium FB question 2', difficulty: 'Medium' },
        ])
        .mockResolvedValueOnce([
          { _id: '7', type: 'open_ended', content: 'Medium OE question 1', difficulty: 'Medium' },
          { _id: '8', type: 'open_ended', content: 'Medium OE question 2', difficulty: 'Medium' },
        ])
        // Advanced difficulty - type diversity calls
        .mockResolvedValueOnce([
          { _id: '9', type: 'multiple_choice', content: 'Advanced MC question', difficulty: 'Advanced' },
        ])
        .mockResolvedValueOnce([])  // No fill_blank Advanced questions
        .mockResolvedValueOnce([
          { _id: '10', type: 'open_ended', content: 'Advanced OE question', difficulty: 'Advanced' },
        ]);

      mockQuestionPoolModel.constructor.mockImplementation((doc) => doc);

      const params: QuestionSelectionParams = {
        count: 10,
        distributionConfig: {
          difficultyDistribution: {
            Easy: 0.2,    // 2 questions
            Medium: 0.6,  // 6 questions
            Advanced: 0.2, // 2 questions
          },
          questionTypeBalancing: {
            enabled: true,
            preferDiversity: true,
          },
          qualityValidation: {
            enabled: false,
            failureHandlingStrategy: 'discard',
            maxReplacementAttempts: 0,
            minValidationSuccessRate: 0,
          },
        },
        skipValidation: true,
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      // We expect 8 questions based on our mock data (2 Easy + 4 Medium + 2 Advanced)
      expect(result.questions).toHaveLength(8);
      expect(result.metadata.totalRequested).toBe(10);
      expect(result.metadata.totalReturned).toBe(8);
      // Check that we have some distribution (exact numbers may vary due to type balancing)
      expect(Object.keys(result.metadata.distributionAchieved)).toContain('Easy');
      expect(Object.keys(result.metadata.distributionAchieved)).toContain('Medium');
      expect(Object.keys(result.metadata.distributionAchieved)).toContain('Advanced');
      // Check actual distribution percentages
      expect(result.metadata.distributionAchieved.Easy).toBe(0.25); // 2/8
      expect(result.metadata.distributionAchieved.Medium).toBe(0.5); // 4/8
      expect(result.metadata.distributionAchieved.Advanced).toBe(0.25); // 2/8
    });

    it('should handle shortfall gracefully with fallback strategies', async () => {
      // Mock distinct method for type diversity
      mockQuestionPoolModel.distinct.mockResolvedValue(['multiple_choice', 'fill_blank', 'open_ended']);

      // Mock insufficient questions for one difficulty level
      mockQuestionPoolModel.aggregate().exec
        .mockResolvedValueOnce([
          { _id: '1', type: 'multiple_choice', content: 'Easy question', difficulty: 'Easy' },
        ]) // Only 1 Easy question instead of 2
        .mockResolvedValueOnce([
          { _id: '2', type: 'fill_blank', content: 'Medium question', difficulty: 'Medium' },
          { _id: '3', type: 'fill_blank', content: 'Medium question 2', difficulty: 'Medium' },
          { _id: '4', type: 'fill_blank', content: 'Medium question 3', difficulty: 'Medium' },
          { _id: '5', type: 'fill_blank', content: 'Medium question 4', difficulty: 'Medium' },
          { _id: '6', type: 'fill_blank', content: 'Medium question 5', difficulty: 'Medium' },
          { _id: '7', type: 'fill_blank', content: 'Medium question 6', difficulty: 'Medium' },
        ])
        .mockResolvedValueOnce([
          { _id: '8', type: 'open_ended', content: 'Advanced question', difficulty: 'Advanced' },
          { _id: '9', type: 'open_ended', content: 'Advanced question 2', difficulty: 'Advanced' },
        ])
        .mockResolvedValueOnce([
          { _id: '10', type: 'multiple_choice', content: 'Additional question', difficulty: 'Medium' },
        ]); // Additional question to fill the gap

      mockQuestionPoolModel.constructor.mockImplementation((doc) => doc);

      const params: QuestionSelectionParams = {
        count: 10,
        distributionConfig: {
          difficultyDistribution: {
            Easy: 0.2,    // 2 questions requested
            Medium: 0.6,  // 6 questions
            Advanced: 0.2, // 2 questions
          },
          fallback: {
            allowBestEffort: true,
            relaxDistributionOnShortfall: true,
            logFallbackReasons: true,
          },
          qualityValidation: {
            enabled: false,
            failureHandlingStrategy: 'discard',
            maxReplacementAttempts: 0,
            minValidationSuccessRate: 0,
          },
        },
        skipValidation: true,
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      // We expect 5 questions based on our mock data (1 Easy + 1 Medium + 2 Advanced + 1 additional)
      expect(result.questions).toHaveLength(5);
      expect(result.metadata.fallbacksTriggered.length).toBeGreaterThan(0);
      // Check that some fallback was triggered (the exact message may vary)
      expect(result.metadata.fallbacksTriggered.length).toBeGreaterThan(0);
    });
  });

  describe('calculateDifficultyTargetCounts', () => {
    it('should calculate correct target counts for difficulty distribution', () => {
      const distribution = { Easy: 0.2, Medium: 0.6, Advanced: 0.2 };
      const result = (service as any).calculateDifficultyTargetCounts(10, distribution);

      expect(result.Easy).toBe(2);
      expect(result.Medium).toBe(6);
      expect(result.Advanced).toBe(2);
    });
  });

  describe('Question Type Balancing', () => {
    it('should balance question types when enabled', async () => {
      // Mock distinct method for type diversity
      mockQuestionPoolModel.distinct.mockResolvedValue(['multiple_choice', 'fill_blank', 'open_ended']);

      // Mock questions with different types
      mockQuestionPoolModel.aggregate().exec
        .mockResolvedValueOnce([
          { _id: '1', type: 'multiple_choice', content: 'MC question 1', difficulty: 'Easy' },
        ])
        .mockResolvedValueOnce([
          { _id: '2', type: 'fill_blank', content: 'FB question 1', difficulty: 'Medium' },
          { _id: '3', type: 'fill_blank', content: 'FB question 2', difficulty: 'Medium' },
          { _id: '4', type: 'fill_blank', content: 'FB question 3', difficulty: 'Medium' },
        ])
        .mockResolvedValueOnce([
          { _id: '5', type: 'open_ended', content: 'OE question 1', difficulty: 'Advanced' },
        ]);

      mockQuestionPoolModel.constructor.mockImplementation((doc) => doc);

      const params: QuestionSelectionParams = {
        count: 5,
        distributionConfig: {
          difficultyDistribution: {
            Easy: 0.2,    // 1 question
            Medium: 0.6,  // 3 questions
            Advanced: 0.2, // 1 question
          },
          questionTypeBalancing: {
            enabled: true,
            preferDiversity: true,
          },
          qualityValidation: {
            enabled: false,
            failureHandlingStrategy: 'discard',
            maxReplacementAttempts: 0,
            minValidationSuccessRate: 0,
          },
        },
        skipValidation: true,
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      // We expect 4 questions based on our mock data (1 Easy + 3 Medium + 0 Advanced due to empty arrays)
      expect(result.questions).toHaveLength(4);
      expect(result.metadata.totalReturned).toBe(4);
    });
  });

  describe('Diversity Algorithms', () => {
    it('should apply diversity penalties to recently selected questions', async () => {
      const recentlySelectedQuestion = {
        _id: '1',
        type: 'multiple_choice',
        content: 'Recently selected question',
        difficulty: 'Easy',
        lastSelectedTimestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
        selectionFrequency: 5,
      };

      const neverSelectedQuestion = {
        _id: '2',
        type: 'multiple_choice',
        content: 'Never selected question',
        difficulty: 'Easy',
        lastSelectedTimestamp: null,
        selectionFrequency: 0,
      };

      const diversityConfig = {
        enabled: true,
        recencyPenaltyWeight: 0.5,
        frequencyPenaltyWeight: 0.3,
        recencyThresholdHours: 24,
      };

      const result = await (service as any).applyDiversityAlgorithms(
        [recentlySelectedQuestion, neverSelectedQuestion],
        diversityConfig
      );

      // The never-selected question should come first due to better diversity score
      expect(result[0]._id).toBe('2');
      expect(result[1]._id).toBe('1');
    });
  });

  describe('Quality Validation', () => {
    it('should validate questions and handle failures according to strategy', async () => {
      const questions = [
        { _id: '1', type: 'multiple_choice', content: 'Valid question', difficulty: 'Easy' },
        { _id: '2', type: 'multiple_choice', content: '', difficulty: 'Easy' }, // Invalid - empty content
      ];

      // Mock validation service to return one valid, one invalid
      mockValidationService.validateQuestions.mockResolvedValue({
        results: [
          { isValid: true, score: 0.9, issues: [] },
          { isValid: false, score: 0.3, issues: [{ type: 'format', severity: 'critical', message: 'Empty content' }] },
        ],
        summary: {
          totalValidated: 2,
          passed: 1,
          failed: 1,
          successRate: 0.5,
          averageScore: 0.6,
        },
        processingTime: 100,
      });

      const validationConfig = {
        enabled: true,
        failureHandlingStrategy: 'discard' as const,
        maxReplacementAttempts: 3,
        minValidationSuccessRate: 0.8,
      };

      const result = await (service as any).applyQualityValidation(
        questions,
        validationConfig,
        {},
        2
      );

      expect(result.validatedQuestions).toHaveLength(1);
      expect(result.validatedQuestions[0]._id).toBe('1');
      expect(result.validationStats.successRate).toBe(0.5);
      expect(result.fallbacksTriggered.length).toBeGreaterThan(0);
    });
  });
});
