import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Logger } from '@nestjs/common';
import { QuestionPoolService } from './question-pool.service';
import { QuestionPool } from '../mongodb/schemas/question-pool.schema';
import { QuestionPoolCacheService } from './services/question-pool-cache.service';
import { QuestionPoolMetricsService } from './services/question-pool-metrics.service';
import { PoolMonitoringService } from '../monitoring/services/pool-monitoring.service';
import { ContentValidationService } from '../validation/content-validation.service';
import { QuestionPoolConfigService } from './question-pool-config.service';
import { QuestionSelectionParams, QuestionSelectionResult } from './interfaces/distribution.interface';

describe('QuestionPoolService', () => {
  let service: QuestionPoolService;
  let mockQuestionPoolModel: any;
  let mockCacheService: jest.Mocked<QuestionPoolCacheService>;
  let mockMetricsService: jest.Mocked<QuestionPoolMetricsService>;
  let mockMonitoringService: jest.Mocked<PoolMonitoringService>;
  let mockContentValidationService: jest.Mocked<ContentValidationService>;
  let mockConfigService: jest.Mocked<QuestionPoolConfigService>;

  // Mock question data for testing
  const mockQuestions = [
    {
      _id: '507f1f77bcf86cd799439011',
      content: 'What is 2 + 2?',
      type: 'multiple_choice',
      options: ['3', '4', '5', '6'],
      answer: '4',
      subject: 'Mathematics',
      parentSubject: 'Mathematics',
      childSubject: 'Addition',
      grade: 'Primary 2',
      language: 'English',
      difficulty: 'Easy',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      _id: '507f1f77bcf86cd799439012',
      content: 'What is 5 × 3?',
      type: 'multiple_choice',
      options: ['12', '15', '18', '20'],
      answer: '15',
      subject: 'Mathematics',
      parentSubject: 'Mathematics',
      childSubject: 'Multiplication',
      grade: 'Primary 3',
      language: 'English',
      difficulty: 'Medium',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      _id: '507f1f77bcf86cd799439013',
      content: 'Solve for x: 2x + 5 = 15',
      type: 'fill_blank',
      answer: '5',
      subject: 'Mathematics',
      parentSubject: 'Mathematics',
      childSubject: 'Algebra',
      grade: 'Secondary 1',
      language: 'English',
      difficulty: 'Advanced',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  beforeEach(async () => {
    // Create mock aggregation pipeline
    const mockAggregate = jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue(mockQuestions),
    });

    // Create mock model
    mockQuestionPoolModel = {
      aggregate: mockAggregate,
      find: jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockQuestions),
      }),
      countDocuments: jest.fn().mockResolvedValue(100),
      distinct: jest.fn().mockResolvedValue(['Easy', 'Medium', 'Advanced']),
    };

    // Create mock services
    mockCacheService = {
      getFromCache: jest.fn(),
      saveToCache: jest.fn(),
      invalidateCache: jest.fn(),
      generateCacheKey: jest.fn(),
    } as any;

    mockMetricsService = {
      recordQuery: jest.fn(),
      recordQueryDuration: jest.fn(),
      recordCacheHit: jest.fn(),
      recordCacheMiss: jest.fn(),
      updateQuestionCounts: jest.fn(),
      recordError: jest.fn(),
    } as any;

    mockMonitoringService = {
      emitEvent: jest.fn(),
      getEvents: jest.fn(),
      calculatePoolUtilization: jest.fn(),
      calculateQuestionReuse: jest.fn(),
      calculateGenerationTimeComparison: jest.fn(),
      calculateValidationMetrics: jest.fn(),
      calculateCacheMetrics: jest.fn(),
      getDashboardMetrics: jest.fn(),
    } as any;

    mockContentValidationService = {
      validateQuestion: jest.fn().mockResolvedValue({ isValid: true, score: 0.9 }),
      validateQuestions: jest.fn().mockResolvedValue([]),
      getValidationRules: jest.fn().mockReturnValue([]),
    } as any;

    mockConfigService = {
      getDistributionConfig: jest.fn().mockReturnValue({}),
      getCacheConfig: jest.fn().mockReturnValue({}),
      getValidationConfig: jest.fn().mockReturnValue({}),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuestionPoolService,
        {
          provide: getModelToken(QuestionPool.name),
          useValue: mockQuestionPoolModel,
        },
        {
          provide: QuestionPoolCacheService,
          useValue: mockCacheService,
        },
        {
          provide: QuestionPoolMetricsService,
          useValue: mockMetricsService,
        },
        {
          provide: PoolMonitoringService,
          useValue: mockMonitoringService,
        },
        {
          provide: ContentValidationService,
          useValue: mockContentValidationService,
        },
        {
          provide: QuestionPoolConfigService,
          useValue: mockConfigService,
        },
        {
          provide: Logger,
          useValue: {
            debug: jest.fn(),
            log: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<QuestionPoolService>(QuestionPoolService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getRandomQuestions (Legacy Method)', () => {
    it('should return random questions with basic filters', async () => {
      const filters = {
        subject: 'Mathematics',
        grade: 'Primary 2',
        type: 'multiple_choice',
      };

      const result = await service.getRandomQuestions(filters, 2);

      expect(result).toHaveLength(2);
      expect(mockQuestionPoolModel.aggregate).toHaveBeenCalled();
      expect(mockMetricsService.recordQuery).toHaveBeenCalledWith('getRandomQuestionsWithDistribution', 'success');
    });

    it('should handle empty filters', async () => {
      const result = await service.getRandomQuestions({}, 1);

      expect(result).toHaveLength(1);
      expect(mockQuestionPoolModel.aggregate).toHaveBeenCalled();
    });

    it('should respect count parameter', async () => {
      mockQuestionPoolModel.aggregate.mockReturnValue({
        exec: jest.fn().mockResolvedValue([mockQuestions[0]]),
      });

      const result = await service.getRandomQuestions({}, 1);

      expect(result).toHaveLength(1);
    });

    it('should handle database errors gracefully', async () => {
      mockQuestionPoolModel.aggregate.mockReturnValue({
        exec: jest.fn().mockRejectedValue(new Error('Database connection failed')),
      });

      await expect(service.getRandomQuestions({}, 1)).rejects.toThrow('Database connection failed');
    });
  });

  describe('getRandomQuestionsWithDistribution', () => {
    it('should return questions with distribution metadata', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 3,
        distributionConfig: {
          difficultyDistribution: {
            Easy: 0.3,
            Medium: 0.4,
            Advanced: 0.3,
          },
        },
      };

      mockCacheService.getFromCache.mockResolvedValue(null);

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result).toBeDefined();
      expect(result.questions).toBeDefined();
      expect(result.metadata).toBeDefined();
      expect(result.metadata.totalRequested).toBe(3);
      expect(mockMetricsService.recordQuery).toHaveBeenCalled();
    });

    it('should use cached results when available', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 2,
      };

      const cachedResult: QuestionSelectionResult = {
        questions: [mockQuestions[0], mockQuestions[1]],
        metadata: {
          totalRequested: 2,
          totalReturned: 2,
          distributionAchieved: { Easy: 1.0 },
          fallbacksTriggered: [],
          validationStats: {
            totalValidated: 2,
            passed: 2,
            failed: 0,
            successRate: 1.0,
          },
          selectionTime: 100,
          cachedAt: new Date(),
        },
      };

      mockCacheService.getFromCache.mockResolvedValue(cachedResult);

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result).toEqual(cachedResult);
      expect(mockMetricsService.recordCacheHit).toHaveBeenCalled();
      expect(mockQuestionPoolModel.aggregate).not.toHaveBeenCalled();
    });

    it('should skip distribution when skipDistribution is true', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 2,
        skipDistribution: true,
      };

      mockCacheService.getFromCache.mockResolvedValue(null);

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(2);
      expect(mockQuestionPoolModel.aggregate).toHaveBeenCalled();
    });

    it('should handle insufficient questions scenario', async () => {
      mockQuestionPoolModel.aggregate.mockReturnValue({
        exec: jest.fn().mockResolvedValue([mockQuestions[0]]), // Only 1 question available
      });

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 5, // Requesting 5 questions
      };

      mockCacheService.getFromCache.mockResolvedValue(null);

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(1);
      expect(result.metadata.totalReturned).toBe(1);
      expect(result.metadata.totalRequested).toBe(5);
    });
  });

  describe('Filter Building', () => {
    it('should build correct MongoDB query from filters', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        parentSubject: 'Mathematics',
        childSubject: 'Addition',
        type: 'multiple_choice',
        grade: 'Primary 2',
        language: 'English',
        count: 1,
        skipDistribution: true,
      };

      mockCacheService.getFromCache.mockResolvedValue(null);

      await service.getRandomQuestionsWithDistribution(params);

      const aggregateCall = mockQuestionPoolModel.aggregate.mock.calls[0][0];
      const matchStage = aggregateCall.find((stage: any) => stage.$match);

      expect(matchStage.$match).toMatchObject({
        subject: 'Mathematics',
        parentSubject: 'Mathematics',
        childSubject: 'Addition',
        type: 'multiple_choice',
        grade: 'Primary 2',
        language: 'English',
        status: 'active',
      });
    });

    it('should handle partial filters', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 1,
        skipDistribution: true,
      };

      mockCacheService.getFromCache.mockResolvedValue(null);

      await service.getRandomQuestionsWithDistribution(params);

      const aggregateCall = mockQuestionPoolModel.aggregate.mock.calls[0][0];
      const matchStage = aggregateCall.find((stage: any) => stage.$match);

      expect(matchStage.$match).toMatchObject({
        subject: 'Mathematics',
        status: 'active',
      });
      expect(matchStage.$match.type).toBeUndefined();
      expect(matchStage.$match.grade).toBeUndefined();
    });
  });

  describe('Distribution Algorithms', () => {
    beforeEach(() => {
      // Reset mocks for distribution tests
      mockCacheService.getFromCache.mockResolvedValue(null);
    });

    it('should enforce difficulty level distribution', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 6,
        distributionConfig: {
          difficultyDistribution: {
            Easy: 0.33,    // 2 questions
            Medium: 0.34,  // 2 questions
            Advanced: 0.33, // 2 questions
          },
        },
      };

      // Mock different aggregation calls for each difficulty level
      let callCount = 0;
      mockQuestionPoolModel.aggregate.mockImplementation(() => ({
        exec: jest.fn().mockImplementation(() => {
          callCount++;
          if (callCount === 1) {
            // First call for Easy questions
            return Promise.resolve([
              { ...mockQuestions[0], difficulty: 'Easy' },
              { ...mockQuestions[0], difficulty: 'Easy', _id: '507f1f77bcf86cd799439014' },
            ]);
          } else if (callCount === 2) {
            // Second call for Medium questions
            return Promise.resolve([
              { ...mockQuestions[1], difficulty: 'Medium' },
              { ...mockQuestions[1], difficulty: 'Medium', _id: '507f1f77bcf86cd799439015' },
            ]);
          } else {
            // Third call for Advanced questions
            return Promise.resolve([
              { ...mockQuestions[2], difficulty: 'Advanced' },
              { ...mockQuestions[2], difficulty: 'Advanced', _id: '507f1f77bcf86cd799439016' },
            ]);
          }
        }),
      }));

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(6);
      expect(mockQuestionPoolModel.aggregate).toHaveBeenCalledTimes(3); // One call per difficulty level
    });

    it('should handle question type balancing', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 4,
        distributionConfig: {
          questionTypeBalancing: {
            enabled: true,
            preferDiversity: true,
          },
        },
      };

      // Mock distinct call to return available question types
      mockQuestionPoolModel.distinct.mockResolvedValue(['multiple_choice', 'fill_blank']);

      // Mock aggregation calls for type balancing
      let callCount = 0;
      mockQuestionPoolModel.aggregate.mockImplementation(() => ({
        exec: jest.fn().mockImplementation(() => {
          callCount++;
          if (callCount === 1) {
            return Promise.resolve([
              { ...mockQuestions[0], type: 'multiple_choice' },
              { ...mockQuestions[0], type: 'multiple_choice', _id: '507f1f77bcf86cd799439017' },
            ]);
          } else {
            return Promise.resolve([
              { ...mockQuestions[2], type: 'fill_blank' },
              { ...mockQuestions[2], type: 'fill_blank', _id: '507f1f77bcf86cd799439018' },
            ]);
          }
        }),
      }));

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(4);
      expect(mockQuestionPoolModel.distinct).toHaveBeenCalledWith('type', expect.any(Object));
    });

    it('should apply diversity mechanisms to prevent repetition', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 2,
        distributionConfig: {
          diversity: {
            enabled: true,
            recencyPenaltyWeight: 0.5,
            frequencyPenaltyWeight: 0.3,
            recencyThresholdHours: 24,
          },
        },
      };

      await service.getRandomQuestionsWithDistribution(params);

      // Verify that diversity mechanisms are applied in the aggregation pipeline
      const aggregateCall = mockQuestionPoolModel.aggregate.mock.calls[0][0];
      expect(aggregateCall).toBeDefined();
      expect(mockMonitoringService.emitEvent).toHaveBeenCalled();
    });

    it('should handle fallback when distribution targets cannot be met', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 10,
        distributionConfig: {
          difficultyDistribution: {
            Easy: 0.5,     // 5 questions
            Medium: 0.3,   // 3 questions
            Advanced: 0.2, // 2 questions
          },
        },
      };

      // Mock insufficient questions for some difficulty levels
      let callCount = 0;
      mockQuestionPoolModel.aggregate.mockImplementation(() => ({
        exec: jest.fn().mockImplementation(() => {
          callCount++;
          if (callCount === 1) {
            // Only 2 Easy questions available instead of 5
            return Promise.resolve([
              { ...mockQuestions[0], difficulty: 'Easy' },
              { ...mockQuestions[0], difficulty: 'Easy', _id: '507f1f77bcf86cd799439019' },
            ]);
          } else if (callCount === 2) {
            // 3 Medium questions as expected
            return Promise.resolve([
              { ...mockQuestions[1], difficulty: 'Medium' },
              { ...mockQuestions[1], difficulty: 'Medium', _id: '507f1f77bcf86cd799439020' },
              { ...mockQuestions[1], difficulty: 'Medium', _id: '507f1f77bcf86cd799439021' },
            ]);
          } else {
            // 2 Advanced questions as expected
            return Promise.resolve([
              { ...mockQuestions[2], difficulty: 'Advanced' },
              { ...mockQuestions[2], difficulty: 'Advanced', _id: '507f1f77bcf86cd799439022' },
            ]);
          }
        }),
      }));

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(7); // 2 + 3 + 2 = 7 instead of 10
      expect(result.metadata.fallbacksTriggered).toBeDefined();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty question pool', async () => {
      mockQuestionPoolModel.aggregate.mockReturnValue({
        exec: jest.fn().mockResolvedValue([]),
      });

      const params: QuestionSelectionParams = {
        subject: 'NonExistentSubject',
        count: 5,
      };

      mockCacheService.getFromCache.mockResolvedValue(null);

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(0);
      expect(result.metadata.totalReturned).toBe(0);
    });

    it('should handle database connection errors with retry logic', async () => {
      let attemptCount = 0;
      mockQuestionPoolModel.aggregate.mockImplementation(() => ({
        exec: jest.fn().mockImplementation(() => {
          attemptCount++;
          if (attemptCount < 3) {
            return Promise.reject(new Error('Connection timeout'));
          }
          return Promise.resolve([mockQuestions[0]]);
        }),
      }));

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 1,
      };

      mockCacheService.getFromCache.mockResolvedValue(null);

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(1);
      expect(attemptCount).toBe(3); // Should retry twice before succeeding
    });

    it('should validate count parameter bounds', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: -1, // Invalid negative count
      };

      mockCacheService.getFromCache.mockResolvedValue(null);

      await expect(service.getRandomQuestionsWithDistribution(params)).rejects.toThrow();
    });

    it('should handle very large count requests', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 10000, // Very large request
      };

      mockCacheService.getFromCache.mockResolvedValue(null);

      const result = await service.getRandomQuestionsWithDistribution(params);

      // Should return available questions without error
      expect(result.questions).toHaveLength(mockQuestions.length);
      expect(result.metadata.totalRequested).toBe(10000);
      expect(result.metadata.totalReturned).toBe(mockQuestions.length);
    });
  });

  describe('Metrics and Monitoring Integration', () => {
    it('should record query metrics', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 2,
      };

      mockCacheService.getFromCache.mockResolvedValue(null);

      await service.getRandomQuestionsWithDistribution(params);

      expect(mockMetricsService.recordQuery).toHaveBeenCalledWith('getRandomQuestionsWithDistribution', 'success');
      expect(mockMetricsService.recordQueryDuration).toHaveBeenCalled();
      expect(mockMetricsService.updateQuestionCounts).toHaveBeenCalled();
    });

    it('should record cache metrics', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 2,
      };

      // Test cache miss
      mockCacheService.getFromCache.mockResolvedValue(null);

      await service.getRandomQuestionsWithDistribution(params);

      expect(mockMetricsService.recordCacheMiss).toHaveBeenCalled();

      // Reset and test cache hit
      jest.clearAllMocks();
      const cachedResult: QuestionSelectionResult = {
        questions: [mockQuestions[0]],
        metadata: {
          totalRequested: 1,
          totalReturned: 1,
          distributionAchieved: { Easy: 1.0 },
          fallbacksTriggered: [],
          validationStats: {
            totalValidated: 1,
            passed: 1,
            failed: 0,
            successRate: 1.0,
          },
          selectionTime: 50,
          cachedAt: new Date(),
        },
      };

      mockCacheService.getFromCache.mockResolvedValue(cachedResult);

      await service.getRandomQuestionsWithDistribution(params);

      expect(mockMetricsService.recordCacheHit).toHaveBeenCalled();
    });

    it('should record monitoring events', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 2,
      };

      mockCacheService.getFromCache.mockResolvedValue(null);

      await service.getRandomQuestionsWithDistribution(params);

      expect(mockMonitoringService.emitEvent).toHaveBeenCalled();
    });
  });
});
