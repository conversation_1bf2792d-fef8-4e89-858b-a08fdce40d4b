import { Controller, Get, Post, Body, Patch, Param, Delete, Request, UseInterceptors, UploadedFile, BadRequestException, Logger, ForbiddenException, NotFoundException, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { SchoolService } from './school.service';
import { CreateSchoolDto } from './dto/create-school.dto';
import { UpdateSchoolDto } from './dto/update-school.dto';
import { UploadExaminationFormatDto } from './dto/upload-examination-format.dto';
import { School } from './entities/school.entity';
import { EUserRole } from '../user/dto/create-user.dto';
import { Public } from "../auth/decorators/public.decorator";
import { AuthGuard } from '../auth/guards/auth.guard';
import { RoleGuard } from '../auth/guards/role.guard';
import { Roles } from '../auth/decorators/role.decorator';
import { RbacExceptions } from '../auth/exceptions/rbac-exceptions';
import { RbacService } from '../auth/services/rbac.service';
import { DocumentsService } from '../documents/documents.service';
import { MultimodalService } from '../multimodal/multimodal.service';
import { NarrativeStructureService } from './services/narrative-structure.service';
import {
  ExtractNarrativeStructureResponseDto,
  BulkExtractNarrativeStructureResponseDto,
  NarrativeStructureResponseDto
} from './dto/narrative-structure.dto';

@ApiTags('Schools')
@Controller('schools')
@UseGuards(AuthGuard, RoleGuard)
@ApiBearerAuth()
export class SchoolController {
  private readonly logger = new Logger(SchoolController.name);

  constructor(
    private readonly schoolService: SchoolService,
    private readonly documentsService: DocumentsService,
    private readonly multimodalService: MultimodalService,
    private readonly narrativeStructureService: NarrativeStructureService,
    private readonly rbacService: RbacService,
  ) {}

  @Post()
  @Roles(EUserRole.ADMIN, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ summary: 'Create a new school' })
  @ApiResponse({ status: 201, description: 'The school has been successfully created.', type: School })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  create(@Body() createSchoolDto: CreateSchoolDto, @Request() req): Promise<School> {
    console.log('createSchoolDto', createSchoolDto);
    return this.schoolService.create(createSchoolDto, req.user.sub, req.user.role);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all schools' })
  @ApiResponse({ status: 200, description: 'Return all schools.', type: [School] })
  async findAll(): Promise<School[]> {
    return this.schoolService.findAll();
  }


  @Patch('my-school')
  @Roles(EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ summary: 'Update own school (for Independent Teachers)' })
  @ApiResponse({ status: 200, description: 'The school has been successfully updated.', type: School })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'School not found or you are not the administrator.' })
  async updateOwnSchool(
    @Body() updateSchoolDto: UpdateSchoolDto,
    @Request() req
  ): Promise<School> {
    return this.schoolService.updateOwnSchool(req.user.sub, updateSchoolDto);
  }

  @Get(':id')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER)
  @ApiOperation({ summary: 'Get a school by id' })
  @ApiResponse({ status: 200, description: 'Return the school.', type: School })
  @ApiResponse({ status: 404, description: 'School not found.' })
  async findOne(@Param('id') id: string, @Request() req): Promise<School> {
    const school = await this.schoolService.findOne(id);

    // Business logic: Non-admin users can only access their own school
    if (!this.rbacService.isAdmin(req.user)) {
      if (!this.rbacService.canAccessSchool(req.user, school.id)) {
        throw RbacExceptions.schoolAccessDenied();
      }
    }

    return school;
  }

  @Patch(':id')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ summary: 'Update a school' })
  @ApiResponse({ status: 200, description: 'The school has been successfully updated.', type: School })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'School not found.' })
  async update(
    @Param('id') id: string,
    @Body() updateSchoolDto: UpdateSchoolDto,
    @Request() req
  ): Promise<School> {
    // Business logic: School managers can only update their own school
    if (this.rbacService.isSchoolManager(req.user)) {
      this.rbacService.validateSchoolManagerAccess(req.user, id);
    }

    return this.schoolService.update(id, updateSchoolDto);
  }

  @Delete(':id')
  @Roles(EUserRole.ADMIN)
  @ApiOperation({ summary: 'Delete a school' })
  @ApiResponse({ status: 200, description: 'The school has been successfully deleted.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'School not found.' })
  remove(@Param('id') id: string, @Request() req): Promise<void> {
    return this.schoolService.remove(id, req.user);
  }

  @Post('examination-format')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.INDEPENDENT_TEACHER)
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a PDF file defining the examination format for a specific school' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'PDF file containing the examination format',
        },
        schoolId: {
          type: 'string',
          description: 'School ID for which the examination format applies',
        },
      },
    },
  })
  @ApiResponse({ status: 201, description: 'The examination format has been successfully uploaded.' })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  async uploadExaminationFormat(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadExaminationFormatDto: UploadExaminationFormatDto,
    @Request() req
  ): Promise<{ success: boolean; message: string; documentId?: string }> {
    try {
      this.logger.debug(`Uploading examination format for school ID: ${uploadExaminationFormatDto.schoolId}`);

      // Validate the file
      if (!file) {
        throw new BadRequestException('No file uploaded');
      }

      // Check file type
      if (!file.mimetype.includes('pdf')) {
        throw new BadRequestException('Only PDF files are allowed');
      }

      // Business logic: School managers and independent teachers can only upload for their own school
      if (this.rbacService.isSchoolManager(req.user)) {
        this.rbacService.validateSchoolManagerAccess(req.user, uploadExaminationFormatDto.schoolId);
      } else if (this.rbacService.isIndependentTeacher(req.user)) {
        // Independent teachers can only upload for their own school
        if (!this.rbacService.canAccessSchool(req.user, uploadExaminationFormatDto.schoolId)) {
          throw new ForbiddenException('Independent teachers can only upload examination formats for their own school');
        }
      }

      // Validate that the school exists
      await this.schoolService.findOne(uploadExaminationFormatDto.schoolId);

      // Extract text from the PDF
      const textContent = await this.multimodalService.extractFullTextFromPdf(file.buffer);

      if (!textContent || textContent.trim().length === 0) {
        throw new BadRequestException('Failed to extract text from the PDF file');
      }

      this.logger.debug(`Successfully extracted ${textContent.length} characters from PDF`);

      // Store the examination format in Pinecone
      const documentId = await this.documentsService.storeExaminationFormat(
        uploadExaminationFormatDto.schoolId,
        textContent
      );

      return {
        success: true,
        message: 'Examination format uploaded successfully',
        documentId
      };
    } catch (error) {
      this.logger.error(`Error uploading examination format: ${error.message}`, error.stack);

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException(`Failed to upload examination format: ${error.message}`);
    }
  }

  @Get(':schoolId/examination-format')
  @Roles(EUserRole.ADMIN)
  @ApiOperation({ summary: 'Get the examination format for a school' })
  @ApiResponse({ status: 200, description: 'Return the examination format text.', type: String })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'School or examination format not found.' })
  async getExaminationFormat(
    @Param('schoolId') schoolId: string
  ): Promise<string> {
    // Validate that the school exists
    await this.schoolService.findOne(schoolId);

    // Get the examination format
    const formatText = await this.documentsService.getExaminationFormat(schoolId);

    // If format text is not found, throw NotFoundException
    if (!formatText) {
      throw new NotFoundException('Examination format not found for this school');
    }

    return formatText;
  }

  @Delete(':schoolId/examination-format')
  @Roles(EUserRole.ADMIN)
  @ApiOperation({ summary: 'Delete the examination format for a school' })
  @ApiResponse({ status: 200, description: 'The examination format has been successfully deleted.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'School or examination format not found.' })
  async deleteExaminationFormat(
    @Param('schoolId') schoolId: string
  ): Promise<{ success: boolean; message: string }> {
    // Validate that the school exists
    await this.schoolService.findOne(schoolId);

    // Delete the examination format
    const deleted = await this.documentsService.deleteExaminationFormat(schoolId);

    // If deletion was unsuccessful, throw NotFoundException
    if (!deleted) {
      throw new NotFoundException('Examination format not found for this school');
    }

    return {
      success: true,
      message: 'Examination format deleted successfully'
    };
  }

  @Post(':schoolId/narrative-structure/extract')
  @Roles(EUserRole.ADMIN)
  @ApiOperation({ summary: 'Extract narrative structure from examination formats for a specific school' })
  @ApiResponse({
    status: 201,
    description: 'Narrative structure extracted successfully',
    type: ExtractNarrativeStructureResponseDto
  })
  @ApiResponse({ status: 400, description: 'Bad request - No examination formats found or extraction failed' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'School not found' })
  async extractNarrativeStructure(
    @Param('schoolId') schoolId: string
  ): Promise<ExtractNarrativeStructureResponseDto> {

    try {
      this.logger.log(`Starting narrative structure extraction for school ${schoolId}`);

      const result = await this.narrativeStructureService.extractForSchool(schoolId);

      if (result.success && result.narrativeStructure) {
        return {
          success: true,
          message: 'Narrative structure extracted successfully',
          narrativeStructure: {
            id: result.narrativeStructure.id,
            schoolId: result.narrativeStructure.schoolId,
            content: result.narrativeStructure.content,
            sourceFormatsCount: result.narrativeStructure.sourceFormatsCount,
            extractedAt: result.narrativeStructure.extractedAt,
            version: result.narrativeStructure.version,
            extractionMetadata: result.narrativeStructure.extractionMetadata,
            createdAt: result.narrativeStructure.createdAt,
            updatedAt: result.narrativeStructure.updatedAt,
          },
          processedFormatsCount: result.narrativeStructure.sourceFormatsCount,
        };
      } else {
        return {
          success: false,
          message: result.error || 'Failed to extract narrative structure',
        };
      }
    } catch (error) {
      this.logger.error(`Error extracting narrative structure for school ${schoolId}: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to extract narrative structure: ${error.message}`);
    }
  }

  @Post('narrative-structure/extract-all')
  @Roles(EUserRole.ADMIN)
  @ApiOperation({ summary: 'Extract narrative structures for all schools with examination formats' })
  @ApiResponse({
    status: 201,
    description: 'Bulk narrative structure extraction completed',
    type: BulkExtractNarrativeStructureResponseDto
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  async extractAllNarrativeStructures(): Promise<BulkExtractNarrativeStructureResponseDto> {

    try {
      this.logger.log('Starting bulk narrative structure extraction for all schools');

      const result = await this.narrativeStructureService.extractForAllSchools();

      return {
        success: true,
        message: 'Bulk narrative structure extraction completed',
        totalSchools: result.totalSchools,
        successfulExtractions: result.successfulExtractions,
        failedExtractions: result.failedExtractions,
        results: result.results,
      };
    } catch (error) {
      this.logger.error(`Error during bulk narrative structure extraction: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to perform bulk extraction: ${error.message}`);
    }
  }

  @Get(':schoolId/narrative-structure')
  @Roles(EUserRole.ADMIN)
  @ApiOperation({ summary: 'Get the narrative structure for a school' })
  @ApiResponse({
    status: 200,
    description: 'Return the narrative structure',
    type: NarrativeStructureResponseDto
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'School or narrative structure not found' })
  async getNarrativeStructure(
    @Param('schoolId') schoolId: string
  ): Promise<NarrativeStructureResponseDto> {

    // Validate that the school exists
    await this.schoolService.findOne(schoolId);

    // Get the narrative structure
    const narrativeStructure = await this.narrativeStructureService.findBySchoolId(schoolId);

    if (!narrativeStructure) {
      throw new NotFoundException('Narrative structure not found for this school');
    }

    return {
      id: narrativeStructure.id,
      schoolId: narrativeStructure.schoolId,
      content: narrativeStructure.content,
      sourceFormatsCount: narrativeStructure.sourceFormatsCount,
      extractedAt: narrativeStructure.extractedAt,
      version: narrativeStructure.version,
      extractionMetadata: narrativeStructure.extractionMetadata,
      createdAt: narrativeStructure.createdAt,
      updatedAt: narrativeStructure.updatedAt,
    };
  }

  @Delete(':schoolId/narrative-structure')
  @Roles(EUserRole.ADMIN)
  @ApiOperation({ summary: 'Delete the narrative structure for a school' })
  @ApiResponse({ status: 200, description: 'Narrative structure deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'School or narrative structure not found' })
  async deleteNarrativeStructure(
    @Param('schoolId') schoolId: string
  ): Promise<{ success: boolean; message: string }> {

    // Validate that the school exists
    await this.schoolService.findOne(schoolId);

    // Delete the narrative structure
    const deleted = await this.narrativeStructureService.deleteBySchoolId(schoolId);

    if (!deleted) {
      throw new NotFoundException('Narrative structure not found for this school');
    }

    return {
      success: true,
      message: 'Narrative structure deleted successfully'
    };
  }
}
