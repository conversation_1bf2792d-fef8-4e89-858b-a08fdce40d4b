import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../../redis/redis.service';
import { PermissionService } from '../../permission/permission.service';
import {
  UsagePeriod,
  UsageLimitCheckResult,
  UsageTrackingConfig,
  DEFAULT_USAGE_TRACKING_CONFIG,
} from '../interfaces/usage-tracking.interface';
import { UNLIMITED_LIMIT, isUnlimitedLimit } from '../../permission/constants/permission.constants';

@Injectable()
export class UsageTrackingService {
  private readonly logger = new Logger(UsageTrackingService.name);
  private readonly config: UsageTrackingConfig = DEFAULT_USAGE_TRACKING_CONFIG;

  constructor(
    private readonly redisService: RedisService,
    private readonly permissionService: PermissionService,
  ) {}

  /**
   * Increment usage count for a user and feature
   * @param userId - The user ID
   * @param feature - The feature being used (e.g., 'maxWorksheets', 'maxStudents')
   * @param amount - Amount to increment (default: 1)
   * @param period - Time period for tracking (default: 'daily')
   * @returns Promise<number> - New usage count
   */
  async incrementUsage(
    userId: string,
    feature: string,
    amount: number = 1,
    period: UsagePeriod = 'daily',
  ): Promise<number> {
    this.logger.debug(
      `Incrementing usage for user ${userId}, feature ${feature}, amount ${amount}, period ${period}`,
    );

    try {
      const redisKey = this.generateRedisKey(userId, feature, period);
      const ttl = this.config.ttl[period];

      const redisClient = this.redisService.getClient('COMMON_CACHE_NAME');

      // Use INCRBY for atomic increment
      const newCount = await redisClient.incrby(redisKey, amount);

      // Set TTL if this is a new key (count equals the increment amount)
      if (newCount === amount) {
        await redisClient.expire(redisKey, ttl);
        this.logger.debug(`Set TTL ${ttl}s for new usage key: ${redisKey}`);
      }

      this.logger.debug(
        `Usage incremented for user ${userId}, feature ${feature}: ${newCount}`,
      );

      return newCount;
    } catch (error) {
      this.logger.error(
        `Error incrementing usage for user ${userId}, feature ${feature}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get current usage count for a user and feature
   * @param userId - The user ID
   * @param feature - The feature to check
   * @param period - Time period for tracking (default: 'daily')
   * @returns Promise<number> - Current usage count
   */
  async getCurrentUsage(
    userId: string,
    feature: string,
    period: UsagePeriod = 'daily',
  ): Promise<number> {
    this.logger.debug(
      `Getting current usage for user ${userId}, feature ${feature}, period ${period}`,
    );

    try {
      const redisKey = this.generateRedisKey(userId, feature, period);
      const redisClient = this.redisService.getClient('COMMON_CACHE_NAME');

      const count = await redisClient.get(redisKey);
      const usage = count ? parseInt(count, 10) : 0;

      this.logger.debug(
        `Current usage for user ${userId}, feature ${feature}: ${usage}`,
      );

      return usage;
    } catch (error) {
      this.logger.error(
        `Error getting current usage for user ${userId}, feature ${feature}: ${error.message}`,
        error.stack,
      );
      // Return 0 on error to allow graceful degradation
      return 0;
    }
  }

  /**
   * Check if usage limit has been reached
   * @param userId - The user ID
   * @param feature - The feature to check
   * @param period - Time period for tracking (default: 'daily')
   * @returns Promise<boolean> - True if limit is reached, false otherwise
   */
  async isUsageLimitReached(
    userId: string,
    feature: string,
    period: UsagePeriod = 'daily',
  ): Promise<boolean> {
    const checkResult = await this.checkUsageLimit(userId, feature, period);
    return !checkResult.withinLimit;
  }

  /**
   * Check usage against limit with detailed information
   * @param userId - The user ID
   * @param feature - The feature to check
   * @param period - Time period for tracking (default: 'daily')
   * @returns Promise<UsageLimitCheckResult> - Detailed limit check result
   */
  async checkUsageLimit(
    userId: string,
    feature: string,
    period: UsagePeriod = 'daily',
  ): Promise<UsageLimitCheckResult> {
    this.logger.debug(
      `Checking usage limit for user ${userId}, feature ${feature}, period ${period}`,
    );

    try {
      // Get current usage
      const currentUsage = await this.getCurrentUsage(userId, feature, period);

      // Get user's limit from permission service
      const limit = await this.permissionService.getLimit(userId, feature);

      if (limit === null) {
        this.logger.warn(
          `No limit defined for user ${userId}, feature ${feature}. Denying access.`,
        );
        return {
          withinLimit: false,
          current: currentUsage,
          limit: 0,
          remaining: 0,
          period,
          feature,
        };
      }

      // Handle unlimited limit
      if (isUnlimitedLimit(limit)) {
        this.logger.debug(
          `Unlimited limit for user ${userId}, feature ${feature}. Always within limit.`,
        );
        return {
          withinLimit: true,
          current: currentUsage,
          limit: UNLIMITED_LIMIT,
          remaining: UNLIMITED_LIMIT, // -1 indicates unlimited remaining
          period,
          feature,
        };
      }

      const withinLimit = currentUsage < limit;
      const remaining = Math.max(0, limit - currentUsage);

      const result: UsageLimitCheckResult = {
        withinLimit,
        current: currentUsage,
        limit,
        remaining,
        period,
        feature,
      };

      this.logger.debug(
        `Usage limit check for user ${userId}, feature ${feature}: ${JSON.stringify(result)}`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Error checking usage limit for user ${userId}, feature ${feature}: ${error.message}`,
        error.stack,
      );

      // Return restrictive result on error
      return {
        withinLimit: false,
        current: 0,
        limit: 0,
        remaining: 0,
        period,
        feature,
      };
    }
  }

  /**
   * Reset usage count for a user and feature (useful for testing or admin operations)
   * @param userId - The user ID
   * @param feature - The feature to reset
   * @param period - Time period for tracking (default: 'daily')
   * @returns Promise<boolean> - True if reset was successful
   */
  async resetUsage(
    userId: string,
    feature: string,
    period: UsagePeriod = 'daily',
  ): Promise<boolean> {
    this.logger.debug(
      `Resetting usage for user ${userId}, feature ${feature}, period ${period}`,
    );

    try {
      const redisKey = this.generateRedisKey(userId, feature, period);
      const redisClient = this.redisService.getClient('COMMON_CACHE_NAME');

      const result = await redisClient.del(redisKey);
      const success = result > 0;

      this.logger.debug(
        `Usage reset for user ${userId}, feature ${feature}: ${success}`,
      );

      return success;
    } catch (error) {
      this.logger.error(
        `Error resetting usage for user ${userId}, feature ${feature}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Generate Redis key for usage tracking
   * @private
   */
  private generateRedisKey(
    userId: string,
    feature: string,
    period: UsagePeriod,
  ): string {
    const now = new Date();
    const prefix = this.config.keyPrefixes[period];

    let periodKey: string;
    switch (period) {
      case 'daily':
        periodKey = now.toISOString().split('T')[0]; // YYYY-MM-DD
        break;
      case 'weekly':
        const year = now.getFullYear();
        const week = this.getWeekNumber(now);
        periodKey = `${year}-W${week.toString().padStart(2, '0')}`;
        break;
      case 'monthly':
        periodKey = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`;
        break;
      default:
        throw new Error(`Unsupported period: ${period}`);
    }

    return `${prefix}${periodKey}:${userId}:${feature}`;
  }

  /**
   * Get ISO week number
   * @private
   */
  private getWeekNumber(date: Date): number {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil(((d.getTime() - yearStart.getTime()) / 86400000 + 1) / 7);
  }
}
