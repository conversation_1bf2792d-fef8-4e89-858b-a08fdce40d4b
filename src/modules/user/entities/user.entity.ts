import { <PERSON><PERSON><PERSON>, Column, BeforeInsert, BeforeUpdate, ManyToOne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { EUserRole, UserStatus } from '../dto/create-user.dto'; // Import the EUserRole enum and UserStatus
import BaseEntity from 'src/core/entities/base-entity';
import { Bcrypt } from 'src/core/utils/bcrypt';
import { School } from 'src/modules/school/entities/school.entity';

@Entity('users') // Specify the table name
export class User extends BaseEntity {
  @Column({ length: 100, nullable: true })
  name?: string;

  @Column({ unique: true })
  email: string;

  @Column({ type: 'enum', enum: EUserRole }) // Use enum type for the role
  role: EUserRole;

  @Column({ nullable: true })
  password?: string;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE
  })
  status: UserStatus;

  @ManyToOne(() => School, { nullable: true })
  @JoinColumn({ name: 'schoolId' })
  school: School;

  @Column({ nullable: true })
  schoolId?: string | null;

  @Column({ nullable: true })
  passwordResetToken?: string;

  @Column({ type: 'timestamp with time zone', nullable: true })
  passwordResetExpires?: Date;

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.password && !this.password.startsWith('$2')) {
      console.log('Hashing password during entity lifecycle hook'); // Debug log
      this.password = await Bcrypt.hash(this.password);
    }
  }
}
