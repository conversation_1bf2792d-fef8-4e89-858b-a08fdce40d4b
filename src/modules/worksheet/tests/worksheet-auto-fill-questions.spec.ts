import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getModelToken } from '@nestjs/mongoose';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { WorksheetQuestionService } from '../services/worksheet-question.service';
import { QuestionPoolService } from '../../question-pool/question-pool.service';
import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { WorksheetPromptResult } from '../../mongodb/schemas/worksheet-prompt-result.schema';
import { EUserRole } from '../../user/dto/create-user.dto';
import { AutoFillQuestionsDto } from '../dto/worksheet-question.dto';

describe('WorksheetQuestionService - Auto Fill Questions', () => {
  let service: WorksheetQuestionService;
  let questionPoolService: QuestionPoolService;
  let worksheetRepository: any;
  let questionDocumentModel: any;
  let promptResultModel: any;

  const mockUser = {
    sub: 'user-123',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'school-123'
  };

  const mockWorksheet = {
    id: 'worksheet-123',
    title: 'Test Worksheet',
    questionIds: ['q1', 'q2'], // Current has 2 questions
    selectedOptions: [
      {
        optionType: { key: 'subject' },
        optionValue: { value: 'Mathematics' }
      },
      {
        optionType: { key: 'grade' },
        optionValue: { value: 'Grade 10' }
      },
      {
        optionType: { key: 'difficulty_level' },
        optionValue: { value: 'Medium' }
      },
      {
        optionType: { key: 'question_type' },
        optionValue: { value: 'multiple_choice' }
      }
    ]
  };

  const mockPoolQuestions = {
    questions: [
      {
        _id: 'pool-q1',
        type: 'multiple_choice',
        content: 'What is 2 + 2?',
        options: ['3', '4', '5', '6'],
        answer: ['4'],
        explain: 'Basic addition',
        subject: 'Mathematics',
        parentSubject: 'Primary Mathematics',
        childSubject: 'Arithmetic',
        grade: 'Grade 10',
        difficulty: 'Medium'
      },
      {
        _id: 'pool-q2',
        type: 'multiple_choice',
        content: 'What is 3 × 3?',
        options: ['6', '9', '12', '15'],
        answer: ['9'],
        explain: 'Basic multiplication',
        subject: 'Mathematics',
        parentSubject: 'Primary Mathematics',
        childSubject: 'Arithmetic',
        grade: 'Grade 10',
        difficulty: 'Medium'
      }
    ],
    total: 2
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorksheetQuestionService,
        {
          provide: getRepositoryToken(Worksheet),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getModelToken(WorksheetQuestionDocument.name),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getModelToken(WorksheetPromptResult.name),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: QuestionPoolService,
          useValue: {
            getQuestions: jest.fn(),
          },
        },
        // Mock other dependencies
        { provide: 'WorksheetQuestionAuditService', useValue: {} },
        { provide: 'SocketGateway', useValue: {} },
        { provide: 'WorksheetQuestionCollaborationGateway', useValue: {} },
        { provide: 'WorksheetQuestionLockingService', useValue: {} },
        { provide: 'WorksheetQuestionMetricsService', useValue: {} },
        { provide: 'WorksheetQuestionEnhancedCacheService', useValue: {} },
        { provide: 'UserQuestionHistoryService', useValue: {} },
      ],
    }).compile();

    service = module.get<WorksheetQuestionService>(WorksheetQuestionService);
    questionPoolService = module.get<QuestionPoolService>(QuestionPoolService);
    worksheetRepository = module.get(getRepositoryToken(Worksheet));
    questionDocumentModel = module.get(getModelToken(WorksheetQuestionDocument.name));
    promptResultModel = module.get(getModelToken(WorksheetPromptResult.name));
  });

  describe('autoFillQuestionsFromPool', () => {
    it('should successfully auto-fill questions from pool', async () => {
      // Mock worksheet access validation
      jest.spyOn(service as any, 'validateWorksheetAccess').mockResolvedValue(mockWorksheet);
      
      // Mock criteria extraction
      jest.spyOn(service as any, 'extractCriteriaFromWorksheet').mockResolvedValue({
        subject: 'Mathematics',
        grade: 'Grade 10',
        difficulty: 'Medium',
        questionTypes: ['multiple_choice']
      });

      // Mock question pool service
      questionPoolService.getQuestions = jest.fn().mockResolvedValue(mockPoolQuestions);

      // Mock addQuestionToWorksheet
      jest.spyOn(service, 'addQuestionToWorksheet').mockResolvedValue({
        id: 'new-q1',
        content: 'What is 2 + 2?',
        type: 'multiple_choice',
        options: ['2', '3', '4', '5'],
        answer: ['4'],
        explain: 'Basic addition: 2 + 2 = 4',
        subject: 'Mathematics',
        parentSubject: 'Primary Mathematics',
        childSubject: 'Arithmetic'
      } as any);

      // Mock worksheet update
      worksheetRepository.findOne.mockResolvedValue({
        ...mockWorksheet,
        questionIds: ['q1', 'q2', 'new-q1', 'new-q2'] // Updated with new questions
      });

      const options: AutoFillQuestionsDto = {
        questionCount: 2
      };

      const result = await service.autoFillQuestionsFromPool('worksheet-123', mockUser, options);

      expect(result).toEqual({
        questionsAdded: 2,
        totalQuestions: 4,
        addedQuestions: expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            content: expect.any(String),
            type: expect.any(String),
            options: expect.any(Array),
            answer: expect.any(Array),
            explain: expect.any(String),
            subject: expect.any(String),
            parentSubject: expect.any(String),
            childSubject: expect.any(String)
          })
        ])
      });

      expect(questionPoolService.getQuestions).toHaveBeenCalledWith(
        expect.objectContaining({
          subject: 'Mathematics',
          grade: 'Grade 10',
          difficulty: 'Medium',
          type: { $in: ['multiple_choice'] },
          status: 'active',
          schoolId: 'school-123',
          isPublic: true
        }),
        2,
        0
      );
    });

    it('should throw error when no suitable questions found', async () => {
      jest.spyOn(service as any, 'validateWorksheetAccess').mockResolvedValue(mockWorksheet);
      jest.spyOn(service as any, 'extractCriteriaFromWorksheet').mockResolvedValue({
        subject: 'Mathematics',
        grade: 'Grade 10'
      });

      questionPoolService.getQuestions = jest.fn().mockResolvedValue({
        questions: [],
        total: 0
      });

      const options: AutoFillQuestionsDto = {
        questionCount: 2
      };

      await expect(
        service.autoFillQuestionsFromPool('worksheet-123', mockUser, options)
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle difficulty and question type overrides', async () => {
      jest.spyOn(service as any, 'validateWorksheetAccess').mockResolvedValue(mockWorksheet);
      jest.spyOn(service as any, 'extractCriteriaFromWorksheet').mockResolvedValue({
        subject: 'Mathematics',
        grade: 'Grade 10',
        difficulty: 'Easy', // Original difficulty
        questionTypes: ['true_false'] // Original types
      });

      questionPoolService.getQuestions = jest.fn().mockResolvedValue(mockPoolQuestions);
      jest.spyOn(service, 'addQuestionToWorksheet').mockResolvedValue({} as any);
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);

      const options: AutoFillQuestionsDto = {
        questionCount: 1,
        difficultyOverride: 'Advanced', // Override difficulty
        questionTypesOverride: ['multiple_choice', 'calculation'] // Override types
      };

      await service.autoFillQuestionsFromPool('worksheet-123', mockUser, options);

      expect(questionPoolService.getQuestions).toHaveBeenCalledWith(
        expect.objectContaining({
          difficulty: 'Advanced', // Should use override
          type: { $in: ['multiple_choice', 'calculation'] } // Should use override
        }),
        1,
        0
      );
    });
  });
});
